#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试 MLX-VLM 输出格式
"""

import subprocess

# 配置
DEFAULT_MLX_MODEL = "/Users/<USER>/Desktop/work/ai_models/lm_studio/mlx-community/Qwen2.5-VL-7B-Instruct-4bit"
TEST_IMAGE_PATH = "/Users/<USER>/Desktop/before_work/Image_uploader/shared/Image_files/cuijie12/2025-08-29/外卖推荐理由中仅有text为空.jpg"

def debug_mlx_output():
    """调试 MLX-VLM 的原始输出"""
    print("=" * 80)
    print("调试 MLX-VLM 原始输出格式")
    print("=" * 80)
    
    prompt = "请简单描述这张图片。"
    
    cmd = [
        "mlx_vlm.generate",
        "--prompt", prompt,
        "--image", TEST_IMAGE_PATH,
        "--model", DEFAULT_MLX_MODEL,
        "--max-tokens", "100",
        "--temp", "0.3"
    ]
    
    print(f"执行命令: {' '.join(cmd[:3])} ...")
    
    result = subprocess.run(
        cmd,
        capture_output=True,
        text=True,
        timeout=120
    )
    
    if result.returncode != 0:
        print(f"❌ 命令执行失败: {result.stderr}")
        return
    
    output = result.stdout
    
    print("原始输出 (带字符表示):")
    print("=" * 60)
    for i, char in enumerate(output):
        if char == '\n':
            print(f"\\n[{i}]")
        elif char == '\r':
            print(f"\\r[{i}]")
        elif char == '\t':
            print(f"\\t[{i}]")
        else:
            print(char, end='')
    print()
    print("=" * 60)
    
    print("\n按行分析:")
    print("=" * 60)
    lines = output.split('\n')
    for i, line in enumerate(lines):
        print(f"行 {i:2d}: '{line}'")
    
    print("\n查找关键标记:")
    print("=" * 60)
    
    # 查找 assistant 标记
    if "<|im_start|>assistant" in output:
        start_pos = output.find("<|im_start|>assistant")
        print(f"找到 <|im_start|>assistant 在位置: {start_pos}")
        
        assistant_start = start_pos + len("<|im_start|>assistant")
        assistant_content = output[assistant_start:]
        
        if "<|im_end|>" in assistant_content:
            end_pos = assistant_content.find("<|im_end|>")
            assistant_content = assistant_content[:end_pos]
            print(f"找到 <|im_end|> 在相对位置: {end_pos}")
        
        print(f"\nassistant 内容 (长度: {len(assistant_content)}):")
        print("'" + assistant_content + "'")
        
        # 分析 assistant 内容的每个字符
        print(f"\nassistant 内容字符分析:")
        for i, char in enumerate(assistant_content[:200]):  # 只显示前200个字符
            if char == '\n':
                print(f"\\n[{i}]")
            elif char == '\r':
                print(f"\\r[{i}]")
            elif char == '\t':
                print(f"\\t[{i}]")
            elif char == ' ':
                print(f"_[{i}]", end='')
            else:
                print(char, end='')
        print("...")
    
    # 查找分隔符
    separator_count = output.count("==========")
    print(f"\n找到 ========== 分隔符 {separator_count} 次")
    
    # 查找警告信息
    if "Warning:" in output:
        warning_pos = output.find("Warning:")
        print(f"找到 Warning: 在位置: {warning_pos}")
        
        # 查找警告信息的结束位置
        warning_line_end = output.find('\n', warning_pos)
        if warning_line_end != -1:
            warning_text = output[warning_pos:warning_line_end]
            print(f"警告文本: '{warning_text}'")
            
            # 查看警告后的内容
            after_warning = output[warning_line_end+1:warning_line_end+100]
            print(f"警告后的内容 (前100字符): '{after_warning}'")

if __name__ == "__main__":
    debug_mlx_output()
