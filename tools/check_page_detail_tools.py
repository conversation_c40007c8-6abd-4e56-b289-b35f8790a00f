#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于MLX-VLM的页面检查工具，用于检测页面展示问题和查找元素信息。

提供 `check_ui_bugs` 函数供 agent 使用。
提供 `locate_element_from_layout` 函数供 agent 使用。
提供 `analysis_now_page` 函数供 agent 使用。

使用MLX-VLM框架，支持Qwen2.5-VL等视觉语言模型。
"""

import os
import time
import sys
import subprocess
from typing import Dict, Any, Optional
from PIL import Image

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入现有工具
from tools.screenshot_tools import get_screenshot
from tools._get_page_layout_tools import get_page_layout
from tools._concurrent_log_manager import get_current_task_log_manager

# MLX-VLM配置
DEFAULT_MLX_MODEL = "/Users/<USER>/Desktop/work/ai_models/lm_studio/mlx-community/Qwen2.5-VL-7B-Instruct-4bit"
ALTERNATIVE_MLX_MODEL = "/Users/<USER>/Desktop/work/ai_models/lm_studio/mlx-community/Qwen2.5-VL-3B-Instruct-4bit"  # 备用模型



def _call_mlx_vlm_api(
    image_path: str,
    prompt: str,
    model: str = DEFAULT_MLX_MODEL,
    max_tokens: int = 1024,
    temperature: float = 0.3
) -> Dict[str, Any]:
    """调用MLX-VLM API"""
    start_time = time.time()

    try:
        get_current_task_log_manager().info_tools(f"开始调用MLX-VLM API，模型: {model}", "_call_mlx_vlm_api")

        # 构建MLX-VLM命令
        cmd = [
            "mlx_vlm.generate",
            "--prompt", prompt,
            "--image", image_path,
            "--model", model,
            "--max-tokens", str(max_tokens),
            "--temp", str(temperature)
        ]

        get_current_task_log_manager().info_tools(f"执行命令: {' '.join(cmd)}", "_call_mlx_vlm_api")

        # 执行命令
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=120  # 2分钟超时
        )

        if result.returncode != 0:
            error_msg = f"MLX-VLM命令执行失败: {result.stderr}"
            get_current_task_log_manager().error_tools(error_msg, "_call_mlx_vlm_api")
            return {
                "content": f"命令执行失败: {result.stderr}",
                "elapsed_time": time.time() - start_time,
                "success": False,
                "error": result.stderr,
                "model": model
            }

        # 解析输出，提取生成的文本
        output = result.stdout

        # MLX-VLM的输出格式分析：
        # 1. 包含 ==========分隔符
        # 2. 实际内容在 <|im_start|>assistant 和 <|im_end|> 之间
        # 3. 可能包含警告信息需要跳过

        content = ""

        # 首先尝试通过 assistant 标记提取
        if "<|im_start|>assistant" in output:
            assistant_start = output.find("<|im_start|>assistant") + len("<|im_start|>assistant")
            assistant_content = output[assistant_start:].strip()

            # 移除结束标记
            if "<|im_end|>" in assistant_content:
                assistant_content = assistant_content[:assistant_content.find("<|im_end|>")].strip()

            # 移除警告信息，查找实际内容
            # 根据调试结果，警告信息的格式是：
            # \n\nWarning: Failed to process inputs...\n实际内容...
            lines = assistant_content.split('\n')
            content_lines = []

            for line in lines:
                # 跳过空行和警告行
                if not line.strip() or line.strip().startswith("Warning:"):
                    continue
                # 收集实际内容行
                content_lines.append(line)

            content = '\n'.join(content_lines).strip()

        # 如果 assistant 方法失败，尝试分隔符方法
        if not content:
            lines = output.split('\n')
            content_lines = []
            in_content = False

            for line in lines:
                if line.strip() == "==========":
                    if in_content:
                        break
                    else:
                        in_content = True
                        continue

                if (in_content and
                    not line.startswith("Prompt:") and
                    not line.startswith("Generation:") and
                    not line.startswith("Peak memory:") and
                    not line.startswith("Files:") and
                    not line.startswith("<|im_start|>") and
                    not line.startswith("Warning:")):
                    content_lines.append(line)

            content = '\n'.join(content_lines).strip()

        # 最后的备用方案
        if not content:
            content = output.strip()

        inference_time = time.time() - start_time
        get_current_task_log_manager().info_tools(f"MLX-VLM API调用完成，耗时: {inference_time:.2f}秒", "_call_mlx_vlm_api")

        return {
            "content": content,
            "elapsed_time": inference_time,
            "success": True,
            "model": model
        }

    except subprocess.TimeoutExpired:
        error_msg = "MLX-VLM API调用超时"
        get_current_task_log_manager().error_tools(error_msg, "_call_mlx_vlm_api")
        return {
            "content": "API调用超时",
            "elapsed_time": time.time() - start_time,
            "success": False,
            "error": "timeout",
            "model": model
        }
    except Exception as e:
        error_msg = f"MLX-VLM API调用异常: {e}"
        get_current_task_log_manager().error_tools(error_msg, "_call_mlx_vlm_api")
        return {
            "content": f"调用异常: {e}",
            "elapsed_time": time.time() - start_time,
            "success": False,
            "error": str(e),
            "model": model
        }

def check_ui_bugs(
    udid: str,
    scene_desc: str = "页面检查",
    model: str = DEFAULT_MLX_MODEL
) -> dict:
    """
    使用MLX-VLM模型检查页面是否有展示问题

    参数:
        udid: 设备的序列号(UDID)
        scene_desc: 场景描述，如 "页面检查" 或 "元素查找"
        model: MLX-VLM模型路径，默认为Qwen2.5-VL-7B-Instruct-4bit

    返回:
        dict: 包含检查结果、截图信息等的字典
    """
    start_time = time.time()

    try:
        get_current_task_log_manager().info_tools(f"开始使用MLX-VLM检查设备 {udid} 的页面，模型: {model}", "check_ui_bugs")
        
        # 获取页面布局 - 直接获取文本格式
        get_current_task_log_manager().info_tools("正在获取页面布局...", "check_ui_bugs")
        layout_file_path = get_page_layout(udid, scene_desc, compressed=True, output_format="text")
            
        get_current_task_log_manager().info_tools(f"页面布局已获取并保存到: {layout_file_path}", "check_ui_bugs")
        
        # 获取页面截图
        get_current_task_log_manager().info_tools("正在获取页面截图...", "check_ui_bugs")
        screenshot_result = get_screenshot(udid)
        image_path = screenshot_result.get("local_path")
        
        if not image_path or not os.path.exists(image_path):
            raise ValueError("截图获取失败，无法进行页面检查")
        
        get_current_task_log_manager().info_tools(f"截图已获取，路径: {image_path}", "check_ui_bugs")
        
        # 读取layout文本文件
        layout_text = None
        if layout_file_path and os.path.exists(layout_file_path):
            with open(layout_file_path, "r", encoding="utf-8") as f:
                layout_text = f.read()
        
        # 构造优化的prompt
        role_prompt = """你是UI测试专家。仔细检查这个移动应用截图：

检查要点：
1. 元素完整性：文字、图片、按钮是否完整显示
2. 布局正确性：元素位置、对齐、间距是否正常
3. 内容可读性：文字清晰度、图片加载状态

结果要求：
- 正常：输出"True"
- 异常：输出"False - [具体问题]"

请直接给出结果，简洁明了。

## 页面布局信息说明
页面布局信息采用文本格式提供，包含页面中所有可见和可交互的UI元素。
格式为：序号. 元素类型 属性信息 位置信息 交互状态

### 元素类型说明（Android平台）：
- **TextView**: 文本显示控件，显示文字内容
- **ImageView**: 图片显示控件，显示图标或图片
- **View**: 通用视图控件，通常作为按钮或可点击区域
- **ViewGroup**: 容器控件，包含其他子控件

### 元素类型说明（iOS平台）：
- **Button**: 按钮控件，可点击执行操作
- **StaticText**: 静态文本控件，显示文字信息
- **SearchField**: 搜索输入框控件
- **Image**: 图片控件，显示图标或图片
- **Other**: 其他类型控件，如容器或特殊控件

### 属性信息：
- **text**: 控件显示的文字内容
- **desc**: 控件的描述信息（Android的content-desc）
- **name**: 控件的名称标识（iOS）
- **label**: 控件的标签信息（iOS）
- **id**: 控件的唯一标识符
- **pos=(x,y)**: 控件在屏幕上的中心点位置坐标
- **size=widthxheight**: 控件的宽度和高度
- **interactive**: 交互状态，如clickable(可点击)、enabled(已启用)、accessible(可访问)等"""

        # 添加布局信息 - 直接使用文本格式
        if layout_text:
            role_prompt += f"\n\n页面Layout结构信息：\n{layout_text}"
            get_current_task_log_manager().info_tools(f"Layout文本格式直接使用，大小: {len(layout_text)}字符", "check_ui_bugs")
        
        # 调用MLX-VLM API
        api_result = _call_mlx_vlm_api(
            image_path=image_path,
            prompt=role_prompt,
            model=model,
            max_tokens=1024
        )

        if not api_result["success"]:
            raise ValueError(f"MLX-VLM API调用失败: {api_result.get('error', '未知错误')}")
        
        # 记录统计信息
        stats = {
            "耗时": f"{api_result['elapsed_time']:.2f} 秒",
            "模型": model,
            "成功": True
        }
        get_current_task_log_manager().info_tools(f"页面检查完成，统计信息: {stats}", "check_ui_bugs")
        
        # 解析响应内容
        content = api_result["content"].strip()
        
        # 处理结果
        if content.startswith("True"):
            result = f"页面展示正常，无问题。详情: {content}"
            get_current_task_log_manager().info_tools("页面检查结果: 正常", "check_ui_bugs")
        else:
            parts = content.split("False", 1)
            if len(parts) > 1:
                description = parts[1].strip()
            else:
                description = content
            result = f"页面展示异常! 问题详情: {description}"
            get_current_task_log_manager().warning_tools(f"页面检查结果: 异常 - {description}", "check_ui_bugs")
        
        # 记录操作统计
        get_current_task_log_manager().info_tools(f"页面检查操作完成，总耗时: {time.time() - start_time:.2f}秒", "check_ui_bugs")
        
        # 返回结构化结果
        return {
            "status": "success",
            "udid": udid,
            "text_result": result,
            "image_url": screenshot_result.get("image_url", ""),
            "local_path": image_path,
            "scene_desc": scene_desc,
            "model": model,
            "layout_file_path": layout_file_path,
            "elapsed_time": time.time() - start_time
        }
            
    except Exception as e:
        error_msg = f"使用MLX-VLM检查页面时发生异常: {e}"
        get_current_task_log_manager().error_tools(error_msg, "check_ui_bugs")
        
        return {
            "status": "error",
            "udid": udid,
            "text_result": f"错误: {error_msg}",
            "image_url": "",
            "local_path": "",
            "scene_desc": scene_desc,
            "model": model,
            "error": error_msg
        }

def locate_element_from_layout(
    udid: str,
    element: str,
    scene_desc: str = "元素查找",
    model: str = DEFAULT_MLX_MODEL
) -> dict:
    """
    使用MLX-VLM模型查找指定元素的详细信息

    参数:
        udid: 设备的序列号(UDID)
        element: 要查找的元素名称
        scene_desc: 场景描述，如 "页面检查" 或 "元素查找"
        model: MLX-VLM模型路径，默认为Qwen2.5-VL-7B-Instruct-4bit

    返回:
        dict: 包含查找结果、截图信息等的字典
    """
    start_time = time.time()

    try:
        get_current_task_log_manager().info_tools(f"开始使用MLX-VLM在设备 {udid} 上查找元素 {element}，模型: {model}", "locate_element_from_layout")
        
        # 获取页面布局 - 直接获取文本格式
        get_current_task_log_manager().info_tools("正在获取页面布局...", "locate_element_from_layout")
        layout_file_path = get_page_layout(udid, scene_desc, compressed=True, output_format="text")
            
        get_current_task_log_manager().info_tools(f"页面布局已获取并保存到: {layout_file_path}", "locate_element_from_layout")
        
        # 获取页面截图
        get_current_task_log_manager().info_tools("正在获取页面截图...", "locate_element_from_layout")
        screenshot_result = get_screenshot(udid)
        image_path = screenshot_result.get("local_path")
        
        if not image_path or not os.path.exists(image_path):
            raise ValueError("截图获取失败，无法进行元素查找")
        
        get_current_task_log_manager().info_tools(f"截图已获取，路径: {image_path}", "locate_element_from_layout")
        
        # 读取layout文本文件
        layout_text = None
        if layout_file_path and os.path.exists(layout_file_path):
            with open(layout_file_path, "r", encoding="utf-8") as f:
                layout_text = f.read()
        
        # 使用经过验证的prompt格式
        element_prompt = f"""看一下这张图片，查找一下其中{element}的位置，具体的页面 layout 信息文本为："""

        # 添加布局信息 - 使用成功的格式
        if layout_text:
            element_prompt += f"\n\n{layout_text}"
            get_current_task_log_manager().info_tools(f"Layout文本格式直接使用，大小: {len(layout_text)}字符", "locate_element_from_layout")
        
        # 调用MLX-VLM API
        api_result = _call_mlx_vlm_api(
            image_path=image_path,
            prompt=element_prompt,
            model=model,
            max_tokens=1024
        )

        if not api_result["success"]:
            raise ValueError(f"MLX-VLM API调用失败: {api_result.get('error', '未知错误')}")
        
        # 记录统计信息
        stats = {
            "耗时": f"{api_result['elapsed_time']:.2f} 秒",
            "模型": model,
            "成功": True
        }
        get_current_task_log_manager().info_tools(f"元素查找完成，统计信息: {stats}", "locate_element_from_layout")
        
        # 解析响应内容
        content = api_result["content"].strip()
        
        get_current_task_log_manager().info_tools(f"元素 '{element}' 查找结果: {content}", "locate_element_from_layout")
        
        # 记录操作统计
        get_current_task_log_manager().info_tools(f"元素查找操作完成，总耗时: {time.time() - start_time:.2f}秒", "locate_element_from_layout")
        
        # 返回结构化结果
        return {
            "status": "success",
            "udid": udid,
            "text_result": content,
            "image_url": screenshot_result.get("image_url", ""),
            "local_path": image_path,
            "element": element,
            "scene_desc": scene_desc,
            "model": model,
            "layout_file_path": layout_file_path,
            "elapsed_time": time.time() - start_time
        }
            
    except Exception as e:
        error_msg = f"使用MLX-VLM查找元素时发生异常: {e}"
        get_current_task_log_manager().error_tools(error_msg, "locate_element_from_layout")
        
        return {
            "status": "error",
            "udid": udid,
            "text_result": f"元素查找失败: {error_msg}",
            "image_url": "",
            "local_path": "",
            "element": element,
            "scene_desc": scene_desc,
            "model": model,
            "error": error_msg
        }

def analysis_now_page(udid: str, action_description: str = "", model: str = DEFAULT_MLX_MODEL) -> dict:
    """
    分析美团app界面截图，识别当前界面特征和界面类型

    参数:
        udid: 设备的序列号(UDID)
        action_description: 进入当前页面的动作描述，如"点击搜索框后"、"滑动页面后"等
        model: MLX-VLM模型路径，默认为Qwen2.5-VL-7B-Instruct-4bit

    返回:
        dict: 包含分析结果的字典
    """
    start_time = time.time()

    try:
        get_current_task_log_manager().info_tools(f"开始分析设备 {udid} 的美团app界面，模型: {model}", "analysis_now_page")
        
        # 获取页面截图
        get_current_task_log_manager().info_tools("正在获取页面截图...", "analysis_now_page")
        screenshot_result = get_screenshot(udid)
        image_path = screenshot_result.get("local_path")
        
        if not image_path or not os.path.exists(image_path):
            raise ValueError("截图获取失败，无法进行界面分析")
        
        get_current_task_log_manager().info_tools(f"截图已获取，路径: {image_path}", "analysis_now_page")
        
        # 构造prompt - 包含动作描述信息
        if action_description:
            prompt = f"这个图片是美团 app 的一个界面，这是在{action_description}进入的页面。请告诉我这个界面的特征，并说明你觉得当前处于哪个界面。"
        else:
            prompt = "这个图片是美团 app 的一个界面，请告诉我这个界面的特征，并说明你觉得当前处于哪个界面。"
        
        # 调用MLX-VLM API
        api_result = _call_mlx_vlm_api(
            image_path=image_path,
            prompt=prompt,
            model=model,
            max_tokens=1024
        )

        if not api_result["success"]:
            raise ValueError(f"MLX-VLM API调用失败: {api_result.get('error', '未知错误')}")
        
        # 记录统计信息
        stats = {
            "耗时": f"{api_result['elapsed_time']:.2f} 秒",
            "模型": model,
            "成功": True
        }
        get_current_task_log_manager().info_tools(f"美团app界面分析完成，统计信息: {stats}", "analysis_now_page")
        
        # 解析响应内容
        content = api_result["content"].strip()
        
        get_current_task_log_manager().info_tools(f"美团app界面分析结果: {content}", "analysis_now_page")
        
        # 记录操作统计
        get_current_task_log_manager().info_tools(f"界面分析操作完成，总耗时: {time.time() - start_time:.2f}秒", "analysis_now_page")
        
        # 返回结构化结果
        return {
            "status": "success",
            "udid": udid,
            "text_result": content,
            "image_url": screenshot_result.get("image_url", ""),
            "local_path": image_path,
            "action_description": action_description,
            "model": model,
            "elapsed_time": time.time() - start_time
        }
            
    except Exception as e:
        error_msg = f"分析设备 {udid} 的美团app界面时发生异常: {e}"
        get_current_task_log_manager().error_tools(error_msg, "analysis_now_page")
        
        return {
            "status": "error",
            "udid": udid,
            "text_result": f"界面分析失败: {error_msg}",
            "image_url": "",
            "local_path": "",
            "action_description": action_description,
            "model": model,
            "error": error_msg,
            "elapsed_time": time.time() - start_time
        }

def test_mlx_vlm_connection(model: str = DEFAULT_MLX_MODEL) -> str:
    """
    测试MLX-VLM模型是否可用

    参数:
        model: 要测试的模型路径

    返回:
        str: 测试结果
    """
    try:
        get_current_task_log_manager().info_tools(f"测试MLX-VLM模型: {model}", "test_mlx_vlm_connection")

        # 创建一个简单的测试图片
        test_image = Image.new('RGB', (100, 100), color='blue')
        test_image_path = "/tmp/test_mlx_vlm.png"
        test_image.save(test_image_path)

        # 测试调用
        result = _call_mlx_vlm_api(
            image_path=test_image_path,
            prompt="请描述这张图片的颜色。",
            model=model,
            max_tokens=50
        )

        # 清理测试图片
        try:
            os.remove(test_image_path)
        except:
            pass

        if result["success"]:
            return f"MLX-VLM模型 {model} 可用。测试响应: {result['content'][:100]}..."
        else:
            return f"MLX-VLM模型测试失败: {result.get('error', '未知错误')}"

    except Exception as e:
        error_msg = f"MLX-VLM模型测试失败: {e}"
        get_current_task_log_manager().error_tools(error_msg, "test_mlx_vlm_connection")
        return error_msg

# 为了向后兼容，保留旧的函数名作为别名
check_page_display_ollama = check_ui_bugs
find_element_detail_ollama = locate_element_from_layout
check_page_display_mlx = check_ui_bugs
find_element_detail_mlx = locate_element_from_layout

if __name__ == "__main__":
    # 测试用例
    udid = "6d6fe149"  # 替换为实际设备ID

    print("正在测试MLX-VLM页面检查功能...")
    result = check_ui_bugs(udid)

    print("\n" + "="*50)
    print("【检查结果】")
    print("="*50)
    print(result)