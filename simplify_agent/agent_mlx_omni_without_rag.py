#!/usr/bin/env python3
"""
独立执行的 Agent 类
用于单独调试测试执行流程，支持解析 JSON 测试计划
使用 MLX-Omni-Server 模型进行 Function Calling
"""

import json
import time
import re
import uuid
import os
import sys
import argparse
import subprocess
import signal
import psutil
import requests
import atexit
from typing import List, Dict, Any, Optional
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage, ToolMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field

# 添加父目录到路径
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 导入并发日志管理器
from tools._concurrent_log_manager import (
    LogManager, 
    get_global_log_manager, 
    set_current_task_log_manager,
    create_task_log_manager,
    cleanup_task_log_manager
)

# 导入工具模块
from tools.check_devices_tools import check_devices_connect_android, check_devices_connect_ios, get_available_device
from tools.wait_tools import wait_for_seconds
from tools.screenshot_tools import get_screenshot
from tools.tap_tools import tap
from tools.slide_tools import slide
from tools.api_ocr_tools import show_page_ocr_result, locate_element_from_ocr
from tools.app_operate_tools import restart_app, background_switch
from tools.smart_input_tools import smart_input_text
from tools.llm_base_tools import start_test, end_test, record_summary, record_issue
from tools._device_status_manage_tools import (
    update_device_status, 
    create_device_status, 
    get_device_status,
)
from tools.check_page_detail_tools import check_ui_bugs, locate_element_from_layout, analysis_now_page


class MLXOmniServerManager:
    """MLX-Omni-Server 进程管理器"""
    
    def __init__(self, port: int = 10240, timeout: int = 30):
        self.port = port
        self.timeout = timeout
        self.process = None
        self.base_url = f"http://127.0.0.1:{port}"
        self.is_running = False
        
        # 注册退出清理
        atexit.register(self.cleanup)
        
    def is_port_occupied(self) -> bool:
        """检查端口是否被占用"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == self.port and conn.status == psutil.CONN_LISTEN:
                    return True
            return False
        except (psutil.AccessDenied, psutil.NoSuchProcess):
            return False
    
    def kill_existing_process(self):
        """杀死占用端口的现有进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if not proc.info['cmdline']:
                        continue
                    
                    cmdline_str = ' '.join(proc.info['cmdline'])
                    # 检查是否是 mlx-omni-server 进程且使用相同端口
                    if ('mlx-omni-server' in cmdline_str and 
                        f'--port {self.port}' in cmdline_str):
                        print(f"发现现有 mlx-omni-server 进程 (PID: {proc.info['pid']})，正在终止...")
                        proc.terminate()
                        proc.wait(timeout=5)
                        print(f"已成功终止进程 {proc.info['pid']}")
                        return True
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue
        except Exception as e:
            print(f"清理现有进程时出错: {e}")
        return False
    
    def start_server(self) -> bool:
        """启动 MLX-Omni-Server"""
        if self.is_running:
            print("MLX-Omni-Server 已在运行中")
            return True
        
        # 检查并清理现有进程
        if self.is_port_occupied():
            print(f"端口 {self.port} 已被占用，尝试清理...")
            self.kill_existing_process()
            time.sleep(2)  # 等待进程完全停止
        
        try:
            print(f"正在启动 MLX-Omni-Server，端口: {self.port}...")
            
            # 启动进程 - 简化输出处理，避免管道阻塞
            self.process = subprocess.Popen(
                ['mlx-omni-server', '--port', str(self.port)],
                stdout=subprocess.DEVNULL,  # 忽略输出避免管道阻塞
                stderr=subprocess.DEVNULL,  # 忽略错误输出
                preexec_fn=os.setsid if hasattr(os, 'setsid') else None,
                env=os.environ.copy()
            )
            print(f"MLX-Omni-Server 进程已启动 (PID: {self.process.pid})")
            
            # 等待服务启动
            if self.wait_for_server():
                self.is_running = True
                print(f"✅ MLX-Omni-Server 启动成功，服务地址: {self.base_url}")
                return True
            else:
                print("❌ MLX-Omni-Server 启动失败或健康检查失败")
                self.cleanup()
                return False
                
        except FileNotFoundError:
            print("❌ 未找到 mlx-omni-server 命令，请确保已正确安装")
            return False
        except Exception as e:
            print(f"❌ 启动 MLX-Omni-Server 失败: {e}")
            self.cleanup()
            return False
    
    def wait_for_server(self) -> bool:
        """等待服务器就绪"""
        print("等待服务器就绪...")
        start_time = time.time()
        
        # 先等待15秒让模型加载
        print("等待模型加载（15秒）...")
        for i in range(15):
            # 检查进程是否还在运行
            if self.process and self.process.poll() is not None:
                print(f"\n进程已退出 (返回码: {self.process.returncode})")
                return False
            
            print(".", end="", flush=True)
            time.sleep(1)
        print("")
        
        # 然后进行健康检查
        while time.time() - start_time < self.timeout:
            try:
                # 检查进程是否还在运行
                if self.process and self.process.poll() is not None:
                    print(f"进程已退出 (返回码: {self.process.returncode})")
                    return False
                
                # 检查模型列表
                response = requests.get(f"{self.base_url}/v1/models", timeout=5)
                if response.status_code == 200:
                    models_data = response.json()
                    if models_data.get('data') and len(models_data['data']) > 0:
                        model_names = [m.get('id', 'unknown') for m in models_data['data']]
                        print(f"服务器就绪，已加载模型: {model_names}")
                        return True
                    else:
                        print("模型尚未加载完成...")
                        
            except requests.RequestException as e:
                print(f"连接检查失败: {e}")
            
            time.sleep(2)
        
        print(f"\n超时等待服务器启动 ({self.timeout}秒)")
        return False
    
    def stop_server(self):
        """停止 MLX-Omni-Server"""
        if not self.is_running:
            return
        
        print("正在停止 MLX-Omni-Server...")
        
        try:
            if self.process:
                # 优雅终止
                if hasattr(os, 'killpg'):
                    os.killpg(os.getpgid(self.process.pid), signal.SIGTERM)
                else:
                    self.process.terminate()
                
                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                    print("✅ MLX-Omni-Server 已优雅停止")
                except subprocess.TimeoutExpired:
                    # 强制终止
                    print("强制终止 MLX-Omni-Server...")
                    if hasattr(os, 'killpg'):
                        os.killpg(os.getpgid(self.process.pid), signal.SIGKILL)
                    else:
                        self.process.kill()
                    self.process.wait()
                    print("✅ MLX-Omni-Server 已强制停止")
                
                self.process = None
                
        except (ProcessLookupError, psutil.NoSuchProcess):
            print("进程已不存在")
        except Exception as e:
            print(f"停止进程时出错: {e}")
        finally:
            self.is_running = False
    
    def cleanup(self):
        """清理资源"""
        self.stop_server()
    
    def __enter__(self):
        """上下文管理器入口"""
        if self.start_server():
            return self
        else:
            raise RuntimeError("无法启动 MLX-Omni-Server")
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()


class AgentAlone:
    """独立执行的 Agent 类 - 用于单独调试测试执行"""
    
    def __init__(self, 
                 base_url: str = "http://127.0.0.1:10240",
                 temperature: float = 0.1,
                 task_id: Optional[str] = None,
                 log_manager: Optional[LogManager] = None,
                 create_round_logs: bool = True,
                 auto_manage_server: bool = True):
        self.base_url = base_url
        self.temperature = temperature
        self.chat_history = []
        self.auto_manage_server = auto_manage_server
        
        # MLX-Omni-Server 进程管理
        self.mlx_server = None
        if auto_manage_server:
            # 从 base_url 提取端口号
            port = 10240  # 默认端口
            if ":" in base_url:
                try:
                    port = int(base_url.split(":")[-1])
                except ValueError:
                    pass
            
            self.mlx_server = MLXOmniServerManager(port=port)
        
        # 任务和日志管理
        self.task_id = task_id or f"agent_alone_{int(time.time())}"
        self.create_round_logs = create_round_logs
        
        # 创建轮次日志目录（如果启用）
        if create_round_logs and not log_manager:
            self.round_id, self.round_log_dir = self._generate_round_info()
            self.log_manager = create_task_log_manager(self.task_id, self.round_log_dir)
            self.log_manager.info_agent(f"创建轮次日志目录: {self.round_log_dir}")
        else:
            self.round_id = None
            self.round_log_dir = None
            self.log_manager = log_manager if log_manager else get_global_log_manager()
        
        # 任务上下文
        self.task_context = None
        self.current_device_udid = None
        
        # 初始化 LLM (使用 OpenAI 兼容接口连接 MLX-Omni-Server)
        self.llm = ChatOpenAI(
            base_url=base_url + "/v1",  # MLX-Omni-Server 的 OpenAI 兼容端点
            api_key="mlx-omni",  # MLX-Omni-Server 使用固定 API key
            model="mlx-community/Qwen3-30B-A3B-Instruct-2507-MLX-4bit",  # 使用可用的 Qwen3-Coder 模型
            temperature=temperature
        )
        
        # 定义工具
        self.tools = self._create_tools()
        
        # 绑定工具到 LLM
        try:
            self.llm_with_tools = self.llm.bind_tools(self.tools)
            self.supports_tools = True
            self.log_manager.info_agent(f"MLX-Omni-Server 模型支持工具调用")
        except Exception as e:
            self.log_manager.error_agent(f"绑定工具失败: {e}")
            self.llm_with_tools = self.llm
            self.supports_tools = False
        
        self.log_manager.info_agent(f"AgentAlone 初始化完成，MLX-Omni-Server 端点: {base_url}, 任务ID: {self.task_id}")
        self.log_manager.info_agent(f"轮次ID: {self.round_id}, 日志目录: {self.round_log_dir}")
        self.log_manager.info_agent(f"可用工具: {[tool.name for tool in self.tools]}")
    
    def _generate_round_info(self) -> tuple:
        """生成轮次ID和日志目录"""
        import glob
        
        # 获取现有轮次编号
        existing_rounds = glob.glob("simplify_agent/log/agent_execute_log/round_*")
        if existing_rounds:
            # 提取最大轮次编号
            max_round = 0
            for round_path in existing_rounds:
                round_name = os.path.basename(round_path)
                if round_name.startswith("round_"):
                    try:
                        round_num = int(round_name.split('_')[1])
                        max_round = max(max_round, round_num)
                    except (IndexError, ValueError):
                        continue
            next_round = max_round + 1
        else:
            next_round = 1
        
        # 生成轮次ID和目录名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        round_id = f"round_{next_round:06d}_{timestamp}"
        round_log_dir = f"simplify_agent/log/agent_execute_log/{round_id}"
        
        return round_id, round_log_dir
    
    def cleanup(self):
        """清理资源"""
        try:
            # 清理设备状态
            self._cleanup_device_status()
            
            # 停止 MLX-Omni-Server
            if self.mlx_server:
                self.mlx_server.cleanup()
            
            # 清理日志管理器
            if self.create_round_logs and self.task_id:
                self.log_manager.info_agent(f"清理任务日志管理器: {self.task_id}")
                cleanup_task_log_manager(self.task_id)
                
        except Exception as e:
            print(f"清理过程中出错: {e}")
    
    def set_task_context(self, task_id: str, round_id: str, port: int):
        """设置任务上下文"""
        try:
            self.task_context = {
                'task_id': task_id,
                'round_id': round_id,
                'port': port,
                'agent_prefix': f"[Agent-{port}]"
            }
            self.log_manager.info_agent(f"Agent-{port} 开始处理任务 {task_id} (轮次: {round_id})")
        except Exception as e:
            self.log_manager.error_agent(f"设置任务上下文失败: {e}")
    
    def reset_device_connections(self):
        """重置设备连接状态"""
        try:
            self.current_device_udid = None
            self.log_manager.info_agent("已重置设备连接状态")
        except Exception as e:
            self.log_manager.error_agent(f"重置设备连接失败: {e}")
    
    def _estimate_token_count(self, text: str) -> int:
        """估算文本的token数量"""
        char_count = len(text)
        estimated_tokens = int(char_count / 3)
        return estimated_tokens
    
    def _calculate_context_usage(self, messages: List) -> Dict[str, Any]:
        """计算当前上下文的使用情况"""
        total_chars = 0
        message_breakdown = []
        
        full_content_list = []
        for i, msg in enumerate(messages):
            content = ""
            if hasattr(msg, 'content'):
                content = str(msg.content)
            else:
                content = str(msg)
            
            chars = len(content)
            tokens = self._estimate_token_count(content)
            total_chars += chars
            full_content_list.append(content)
            
            msg_type = type(msg).__name__
            message_breakdown.append({
                "index": i,
                "type": msg_type,
                "chars": chars,
                "estimated_tokens": tokens,
                "content_preview": content[:100] + "..." if len(content) > 100 else content
            })
        
        total_tokens = self._estimate_token_count("".join(full_content_list))
        context_limit = 40960
        usage_percentage = (total_tokens / context_limit) * 100
        
        return {
            "total_messages": len(messages),
            "total_characters": total_chars,
            "estimated_total_tokens": total_tokens,
            "context_limit": context_limit,
            "usage_percentage": round(usage_percentage, 2),
            "remaining_tokens": context_limit - total_tokens,
            "is_approaching_limit": usage_percentage > 80,
            "is_critical": usage_percentage > 95,
            "message_breakdown": message_breakdown
        }
    
    def _mark_device_testing(self, udid: str, device_name: str = "", platform: str = "") -> bool:
        """标记设备为测试中状态"""
        try:
            current_status = get_device_status(udid)
            
            if not current_status:
                if not device_name or not platform:
                    self.log_manager.error_agent(f"设备 {udid} 不存在，需要提供 device_name 和 platform 参数")
                    return False
                
                create_result = create_device_status(udid, device_name, platform)
                if not create_result:
                    self.log_manager.error_agent(f"创建设备状态失败: {udid}")
                    return False
                
                self.log_manager.info_agent(f"创建新设备状态: {udid} ({device_name}, {platform})")
            
            update_device_status(udid, {
                "status": "testing",
                "start_time": time.time()
            })
            
            self.log_manager.info_agent(f"设备状态更新: {udid} -> testing")
            return True
            
        except Exception as e:
            self.log_manager.error_agent(f"标记设备测试状态失败: {e}")
            return False
    
    def _mark_device_ready(self, udid: str) -> bool:
        """标记设备为就绪状态"""
        try:
            current_status = get_device_status(udid)
            if not current_status:
                self.log_manager.error_agent(f"设备 {udid} 不存在，无法更新状态")
                return False
            
            start_time = current_status.get("start_time", 0)
            test_duration = time.time() - start_time if start_time > 0 else 0
            
            update_device_status(udid, {
                "status": "ready",
                "test_duration": test_duration,
                "start_time": 0.0
            })
            
            self.log_manager.info_agent(f"设备状态更新: {udid} -> ready (测试时长: {test_duration:.1f}秒)")
            return True
            
        except Exception as e:
            self.log_manager.error_agent(f"标记设备就绪状态失败: {e}")
            return False
    
    def _cleanup_device_status(self):
        """清理设备状态"""
        if self.current_device_udid:
            self.log_manager.info_agent(f"开始清理设备状态: {self.current_device_udid}")
            success = self._mark_device_ready(self.current_device_udid)
            if success:
                self.log_manager.info_agent(f"设备状态已恢复: {self.current_device_udid}")
            else:
                self.log_manager.error_agent(f"设备状态恢复失败: {self.current_device_udid}")
            self.current_device_udid = None
        else:
            self.log_manager.info_agent("没有需要清理的设备状态")
    
    # 评价器相关逻辑已移除，专注于测试执行

    def load_test_plan(self, json_file_path: str) -> Optional[tuple]:
        """加载 JSON 测试计划文件并提取详细指令和原始请求"""
        try:
            self.log_manager.info_agent(f"📂 加载测试计划文件: {json_file_path}")
            
            if not os.path.exists(json_file_path):
                self.log_manager.error_agent(f"文件不存在: {json_file_path}")
                return None
            
            with open(json_file_path, 'r', encoding='utf-8') as f:
                plan_data = json.load(f)
            
            # 提取详细指令
            detailed_instruction = plan_data.get("agent_instructions", {}).get("detailed_instruction", "")
            
            if not detailed_instruction:
                self.log_manager.error_agent("JSON 文件中未找到 agent_instructions.detailed_instruction")
                return None
            
            # 提取原始请求
            original_request = plan_data.get("original_request", "")
            
            # 提取计划信息用于日志
            plan_summary = plan_data.get("plan_summary", {})
            plan_id = plan_summary.get("plan_id", "unknown")
            summary = plan_summary.get("summary", "未知测试计划")
            total_steps = plan_summary.get("total_steps", 0)
            
            self.log_manager.info_agent(f"成功加载测试计划:")
            self.log_manager.info_agent(f"   计划ID: {plan_id}")
            self.log_manager.info_agent(f"   摘要: {summary}")
            self.log_manager.info_agent(f"   总步骤数: {total_steps}")
            
            return detailed_instruction, original_request
            
        except json.JSONDecodeError as e:
            self.log_manager.error_agent(f"JSON 解析错误: {e}")
            return None
        except Exception as e:
            self.log_manager.error_agent(f"加载测试计划失败: {e}")
            return None
    
    def execute_test_plan(self, json_file_path: str) -> str:
        """执行 JSON 测试计划"""
        try:
            # 启动 MLX-Omni-Server（如果启用自动管理）
            if self.mlx_server and not self.mlx_server.is_running:
                self.log_manager.info_agent("🚀 正在启动 MLX-Omni-Server...")
                if not self.mlx_server.start_server():
                    error_msg = "MLX-Omni-Server 启动失败，无法执行测试计划"
                    self.log_manager.error_agent(error_msg)
                    return error_msg
                self.log_manager.info_agent("✅ MLX-Omni-Server 启动成功")
            
            # 加载测试计划
            plan_result = self.load_test_plan(json_file_path)
            if not plan_result:
                return "测试计划加载失败"
            
            detailed_instruction, original_request = plan_result
            
            self.log_manager.info_agent("🚀 开始执行测试计划...")
            
            # 为了在 task_structured.log 中显示原始请求，我们修改调用流程
            # 先记录原始请求到日志（不影响 start_task 的调用参数）
            if original_request:
                self.log_manager.info_agent(f"原始用户请求: {original_request}")
                # 但实际调用 chat 时使用 detailed_instruction
                # 日志中"用户输入"部分将显示 detailed_instruction，但原始请求已记录
                result = self.chat(detailed_instruction)
            else:
                # 如果没有原始请求，使用 detailed_instruction
                result = self.chat(detailed_instruction)
            
            self.log_manager.info_agent("测试计划执行完成")
            return result
            
        except Exception as e:
            error_msg = f"执行测试计划失败: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return error_msg
        finally:
            # 清理资源
            try:
                self.cleanup()
            except Exception as cleanup_error:
                print(f"清理过程中出错: {cleanup_error}")
    
    def _create_tools(self) -> List:
        """创建工具列表"""
        
        @tool
        def find_available_device(platform: str) -> str:
            """查找指定平台的可用设备"""
            try:
                udid = get_available_device(platform)
                if udid:
                    device_info = get_device_status(udid)
                    device_name = device_info.get("device_name", "Unknown") if device_info else "Unknown"
                    
                    if self._mark_device_testing(udid, device_name, platform):
                        self.current_device_udid = udid
                        self.log_manager.info_agent(f"📱 自动标记设备为测试中: {udid}")
                        
                        result = {
                            "status": "success",
                            "platform": platform,
                            "udid": udid,
                            "device_name": device_name,
                            "message": f"找到可用的{platform}设备: {udid}，已自动标记为测试中"
                        }
                    else:
                        result = {
                            "status": "error",
                            "platform": platform,
                            "udid": udid,
                            "message": f"找到设备{udid}但标记为测试中状态失败"
                        }
                else:
                    result = {
                        "status": "not_found",
                        "platform": platform,
                        "message": f"没有找到可用的{platform}设备（已连接且状态为ready）"
                    }
                return json.dumps(result, ensure_ascii=False)
            except Exception as e:
                result = {
                    "status": "error",
                    "platform": platform,
                    "error": str(e),
                    "message": f"查找{platform}设备时出错: {str(e)}"
                }
                return json.dumps(result, ensure_ascii=False)
        
        @tool
        def wait_seconds(seconds: int = 5) -> str:
            """等待指定的秒数"""
            return wait_for_seconds(seconds)
        
        @tool
        def take_screenshot(udid: str, description: str = "") -> str:
            """对指定设备进行截图操作"""
            try:
                self.log_manager.info_agent(f"开始为设备 {udid} 进行截图")
                if description:
                    self.log_manager.info_agent(f"截图描述: {description}")
                
                screenshot_result = get_screenshot(udid)
                
                result = {
                    "status": "success",
                    "udid": udid,
                    "local_path": screenshot_result.get("local_path", ""),
                    "image_url": screenshot_result.get("image_url", ""),
                    "description": description,
                    "message": f"成功完成设备 {udid} 的截图操作"
                }
                
                if screenshot_result.get("image_url"):
                    self.log_manager.info_agent(f"截图已上传，URL: {screenshot_result['image_url']}")
                else:
                    self.log_manager.warning_agent("截图上传失败，但本地文件已保存")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"截图操作失败: {str(e)}"
                }
                self.log_manager.error_agent(f"截图操作失败: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def start_device_test(udid: str) -> str:
            """开始设备测试"""
            return start_test(udid)
        
        @tool
        def end_device_test(udid: str) -> str:
            """结束设备测试"""
            return end_test(udid)
        
        @tool
        def tap_device(udid: str, x: int, y: int, description: str = "") -> str:
            """在指定设备上执行点击操作"""
            try:
                self.log_manager.info_agent(f"开始对设备 {udid} 执行点击操作，坐标: ({x}, {y})")
                if description:
                    self.log_manager.info_agent(f"点击描述: {description}")
                
                success = tap(udid, x, y)
                
                if success:
                    result = {
                        "execution_status": "success",
                        "tool_result": f"成功在设备 {udid} 的坐标 ({x}, {y}) 执行点击操作",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "timestamp": time.time(),
                        # 保留向后兼容性
                        "status": "success",
                        "message": f"成功在设备 {udid} 的坐标 ({x}, {y}) 执行点击操作"
                    }
                    self.log_manager.info_agent(f"点击操作成功完成")
                else:
                    result = {
                        "execution_status": "failed",
                        "tool_result": f"在设备 {udid} 的坐标 ({x}, {y}) 点击操作失败",
                        "udid": udid,
                        "x": x,
                        "y": y,
                        "description": description,
                        "timestamp": time.time(),
                        # 保留向后兼容性
                        "status": "failed",
                        "message": f"在设备 {udid} 的坐标 ({x}, {y}) 点击操作失败"
                    }
                    self.log_manager.warning_agent(f"点击操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "execution_status": "error",
                    "tool_result": f"点击操作出现异常: {str(e)}",
                    "udid": udid,
                    "x": x,
                    "y": y,
                    "timestamp": time.time(),
                    "error": str(e),
                    "error_type": "exception",
                    # 保留向后兼容性
                    "status": "error",
                    "message": f"点击操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"点击操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def slide_device(udid: str, from_x: float, from_y: float, to_x: float, to_y: float, duration: float = 0.5, description: str = "") -> str:
            """在指定设备上执行滑动操作
重要说明：坐标必须使用屏幕比例值(0-1之间)，而非像素坐标
- from_x, from_y: 滑动起始位置的屏幕比例坐标 (0-1之间)
- to_x, to_y: 滑动结束位置的屏幕比例坐标 (0-1之间)
            """
            try:
                self.log_manager.info_agent(f"开始对设备 {udid} 执行滑动操作，从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})，持续 {duration}秒")
                if description:
                    self.log_manager.info_agent(f"滑动描述: {description}")
                
                success = slide(udid, from_x, from_y, to_x, to_y, duration)
                
                if success:
                    result = {
                        "status": "success",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"成功在设备 {udid} 上执行滑动操作：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    self.log_manager.info_agent(f"滑动操作成功完成")
                else:
                    result = {
                        "status": "failed",
                        "udid": udid,
                        "from_x": from_x,
                        "from_y": from_y,
                        "to_x": to_x,
                        "to_y": to_y,
                        "duration": duration,
                        "description": description,
                        "message": f"在设备 {udid} 上滑动操作失败：从 ({from_x}, {from_y}) 到 ({to_x}, {to_y})"
                    }
                    self.log_manager.warning_agent(f"滑动操作失败")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "from_x": from_x,
                    "from_y": from_y,
                    "to_x": to_x,
                    "to_y": to_y,
                    "duration": duration,
                    "error": str(e),
                    "message": f"滑动操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"滑动操作异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def find_text_on_page(udid: str) -> str:
            """获取当前页面的OCR结果（带坐标信息），用于查找固定文本的坐标"""
            try:
                self.log_manager.info_agent(f"开始为设备 {udid} 获取页面OCR结果")
                
                ocr_result = show_page_ocr_result(udid)
                text_result = ocr_result.get("final_text", "")
                image_url = ocr_result.get("image_url", "")
                
                result = {
                    "status": "success",
                    "udid": udid,
                    "text_result": text_result,
                    "image_url": image_url
                }
                
                self.log_manager.info_agent(f"设备 {udid} 页面OCR结果获取完成")
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"页面OCR结果获取失败: {str(e)}"
                }
                self.log_manager.error_agent(f"页面OCR结果获取异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def ocr_text_validation(udid: str, target_text: str) -> str:
            """使用OCR校验页面是否包含目标文本（子串匹配，大小写不敏感）"""
            try:
                self.log_manager.info_agent(f"开始OCR文本校验 - 设备: {udid}, 目标文本: '{target_text}'")

                validate_result = locate_element_from_ocr(udid, target_text)

                # 统一返回结构
                validate_status = validate_result.get("status", "success")
                execution_status = "success" if validate_status == "success" else "error"
                
                result = {
                    "execution_status": execution_status,
                    "tool_result": validate_result.get("final_reply", ""),
                    "udid": udid,
                    "target_text": target_text,
                    "found": validate_result.get("found", False),
                    "matches": validate_result.get("matches", []),
                    "image_url": validate_result.get("image_url", ""),
                    "local_path": validate_result.get("local_path", ""),
                    "timestamp": time.time(),
                    # 保留向后兼容性
                    "status": validate_result.get("status", "success"),
                    "final_reply": validate_result.get("final_reply", "")
                }

                self.log_manager.info_agent(f"OCR文本校验完成，found={result['found']}，匹配数={len(result['matches'])}")
                return json.dumps(result, ensure_ascii=False)

            except Exception as e:
                error_result = {
                    "execution_status": "error",
                    "tool_result": "没有找到对应的文本",
                    "udid": udid,
                    "target_text": target_text,
                    "found": False,
                    "matches": [],
                    "image_url": "",
                    "local_path": "",
                    "timestamp": time.time(),
                    "error": str(e),
                    "error_type": "exception",
                    # 保留向后兼容性
                    "status": "error",
                    "final_reply": "没有找到对应的文本",
                    "message": f"OCR文本校验失败: {str(e)}"
                }
                self.log_manager.error_agent(f"OCR文本校验异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def check_page_display(udid: str, scene_desc: str = "页面检查") -> str:
            """使用MLX-VLM模型检查页面展示是否正常"""
            try:
                self.log_manager.info_agent(f"开始检查设备 {udid} 的页面展示")
                if scene_desc:
                    self.log_manager.info_agent(f"场景描述: {scene_desc}")
                
                check_result = check_ui_bugs(udid, scene_desc)
                
                if check_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": check_result["text_result"],
                        "image_url": check_result.get("image_url", ""),
                        "message": f"页面检查失败: {check_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"页面检查失败: {check_result['text_result']}")
                else:
                    text_result = check_result["text_result"]
                    is_normal = "页面展示正常" in text_result
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": check_result.get("image_url", ""),
                        "is_normal": is_normal,
                        "message": f"页面检查完成: {'正常' if is_normal else '异常'}"
                    }
                    self.log_manager.info_agent(f"页面检查完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"页面检查出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"页面检查异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def find_element_on_page(udid: str, element: str, scene_desc: str = "元素查找") -> str:
            """使用MLX-VLM模型在页面上查找指定元素"""
            try:
                self.log_manager.info_agent(f"开始在设备 {udid} 上查找元素: {element}")
                if scene_desc:
                    self.log_manager.info_agent(f"场景描述: {scene_desc}")
                
                find_result = locate_element_from_layout(udid, element, scene_desc)
                
                if find_result["status"] == "error":
                    result = {
                        "status": "error",
                        "udid": udid,
                        "text_result": find_result["text_result"],
                        "image_url": find_result.get("image_url", ""),
                        "message": f"元素查找失败: {find_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"元素查找失败: {find_result['text_result']}")
                else:
                    text_result = find_result["text_result"]
                    found = not text_result.startswith("未找到")
                    result = {
                        "status": "success",
                        "udid": udid,
                        "text_result": text_result,
                        "image_url": find_result.get("image_url", ""),
                        "found": found,
                        "element": element,
                        "message": f"元素查找完成: {'找到' if found else '未找到'}"
                    }
                    self.log_manager.info_agent(f"元素查找完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text_result": "",
                    "image_url": "",
                    "message": f"元素查找出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"元素查找异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def restart_application(udid: str) -> str:
            """重启指定设备上的应用"""
            try:
                self.log_manager.info_agent(f"开始重启设备 {udid} 上的应用")
                
                result = restart_app(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功重启设备 {udid} 上的应用"
                    }
                    self.log_manager.info_agent(f"应用重启成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"重启设备 {udid} 应用失败: {result}"
                    }
                    self.log_manager.warning_agent(f"应用重启失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"重启应用操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"重启应用操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def app_background_switch(udid: str) -> str:
            """让美团应用退到后台，等待3秒后再切回前台"""
            try:
                self.log_manager.info_agent(f"开始执行应用后台切换操作 - 设备: {udid}")
                
                result = background_switch(udid)
                
                if result == "success":
                    response = {
                        "status": "success",
                        "udid": udid,
                        "message": f"成功完成设备 {udid} 的应用后台切换操作"
                    }
                    self.log_manager.info_agent(f"应用后台切换操作成功完成")
                elif result == "unknown_device":
                    response = {
                        "status": "error",
                        "udid": udid,
                        "error": "unknown_device",
                        "message": f"设备 {udid} 不存在或未配置"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 不存在")
                elif result == "device_offline":
                    response = {
                        "status": "error", 
                        "udid": udid,
                        "error": "device_offline",
                        "message": f"设备 {udid} 已掉线"
                    }
                    self.log_manager.error_agent(f"设备 {udid} 掉线")
                else:
                    response = {
                        "status": "failed",
                        "udid": udid,
                        "error": result,
                        "message": f"设备 {udid} 后台切换操作失败: {result}"
                    }
                    self.log_manager.warning_agent(f"应用后台切换操作失败: {result}")
                
                return json.dumps(response, ensure_ascii=False)
                
            except Exception as e:
                error_response = {
                    "status": "error",
                    "udid": udid,
                    "error": str(e),
                    "message": f"后台切换操作出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"后台切换操作异常: {e}")
                return json.dumps(error_response, ensure_ascii=False)
        
        @tool
        def record_agent_summary(summary_text: str, summary_type: str = "步骤总结") -> str:
            """记录Agent的实时流程总结和想法"""
            try:
                self.log_manager.info_agent(f"开始记录Agent流程总结: {summary_type}")
                
                result = record_summary(summary_text, summary_type)
                
                self.log_manager.info_agent(f"Agent流程总结记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "summary_text": summary_text,
                    "summary_type": summary_type,
                    "error": str(e),
                    "message": f"记录Agent流程总结失败: {str(e)}"
                }
                self.log_manager.error_agent(f"记录Agent流程总结异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)
        
        @tool
        def record_test_issue(issue_text: str, issue_type: str = "测试问题", severity: str = "中等") -> str:
            """记录测试过程中发现的问题"""
            try:
                self.log_manager.warning_agent(f"开始记录测试问题: {issue_type}, 严重程度: {severity}")
                
                result = record_issue(issue_text, issue_type, severity)
                
                self.log_manager.warning_agent(f"测试问题记录完成")
                return result
                
            except Exception as e:
                error_result = {
                    "status": "error",
                    "issue_text": issue_text,
                    "issue_type": issue_type,
                    "severity": severity,
                    "error": str(e),
                    "message": f"记录测试问题失败: {str(e)}"
                }
                self.log_manager.error_agent(f"记录测试问题异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def input_text_smart(udid: str, text: str, element_index: int = None) -> str:
            """智能文本输入工具"""
            try:
                self.log_manager.info_agent(f"开始智能文本输入操作 - 设备: {udid}, 文本: '{text}', 元素索引: {element_index}")

                result = smart_input_text(udid, text, element_index)

                self.log_manager.info_agent(f"设备 {udid} 智能文本输入操作完成")
                return result

            except Exception as e:
                error_result = {
                    "status": "error",
                    "udid": udid,
                    "text": text,
                    "element_index": element_index,
                    "error": str(e),
                    "message": f"智能文本输入失败: {str(e)}"
                }
                self.log_manager.error_agent(f"智能文本输入异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        @tool
        def analyze_meituan_page(udid: str, action_description: str = "", model: str = "qwen2.5vl:3b") -> str:
            """分析美团app界面截图，识别当前界面特征和界面类型。
            
            参数说明：
            - udid: 设备UDID
            - action_description: 进入当前页面的动作描述，建议格式如："点击搜索框后"、"滑动页面后"、"重启应用后"等
            - model: 使用的模型名称
            
            使用示例：
            - analyze_meituan_page(udid, "点击外卖按钮后")
            - analyze_meituan_page(udid, "输入搜索文字后")
            """
            try:
                self.log_manager.info_agent(f"开始分析设备 {udid} 的美团app界面，模型: {model}")
                if action_description:
                    self.log_manager.info_agent(f"进入动作描述: {action_description}")
                
                analysis_result = analysis_now_page(udid, action_description, model)
                
                if analysis_result["status"] == "error":
                    result = {
                        "execution_status": "error",
                        "tool_result": analysis_result["text_result"],
                        "udid": udid,
                        "timestamp": time.time(),
                        "error": analysis_result.get("error", "分析失败"),
                        "error_type": "analysis_error",
                        # 保留向后兼容性
                        "status": "error",
                        "text_result": analysis_result["text_result"],
                        "message": f"美团界面分析失败: {analysis_result['text_result']}"
                    }
                    self.log_manager.error_agent(f"美团界面分析失败: {analysis_result['text_result']}")
                else:
                    text_result = analysis_result["text_result"]
                    result = {
                        "execution_status": "success",
                        "tool_result": text_result,
                        "udid": udid,
                        "image_url": analysis_result.get("image_url", ""),
                        "local_path": analysis_result.get("local_path", ""),
                        "action_description": action_description,
                        "model": model,
                        "timestamp": time.time(),
                        # 保留向后兼容性
                        "status": "success",
                        "text_result": text_result,
                        "message": "美团界面分析完成"
                    }
                    self.log_manager.info_agent(f"美团界面分析完成: {text_result[:100]}...")
                
                return json.dumps(result, ensure_ascii=False)
                
            except Exception as e:
                error_result = {
                    "execution_status": "error",
                    "tool_result": f"美团界面分析出现异常: {str(e)}",
                    "udid": udid,
                    "timestamp": time.time(),
                    "error": str(e),
                    "error_type": "exception",
                    # 保留向后兼容性
                    "status": "error",
                    "text_result": "",
                    "message": f"美团界面分析出现异常: {str(e)}"
                }
                self.log_manager.error_agent(f"美团界面分析异常: {e}")
                return json.dumps(error_result, ensure_ascii=False)

        return [find_available_device, start_device_test, end_device_test, wait_seconds, take_screenshot, tap_device, slide_device, find_text_on_page, ocr_text_validation, check_page_display, find_element_on_page, restart_application, app_background_switch, record_agent_summary, record_test_issue, input_text_smart, analyze_meituan_page]

    def _create_system_message(self) -> str:
        """创建系统消息"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")
        
        tools_text = "\n".join(tool_descriptions)
        
        # MLX-Omni-Server 模式：优化的系统提示词，适配工具调用
        return f"""你是一个专业的移动设备自动化测试执行助手。你的任务是严格按照用户提供的结构化测试计划，逐步执行每个测试步骤。

可用工具列表：
{tools_text}

**执行策略：**
1. **按序执行**: 严格按照测试计划中的步骤顺序，逐个调用工具
2. **单步执行**: 每次只调用一个工具，等待结果后再决定下一步
3. **参数传递**: 仔细阅读上一步工具的返回结果，提取所需参数用于下一步

**关键要求：**
- **设备一致性**: 所有操作保持在同一设备(udid)上执行
- **坐标精确性**: tap操作必须使用find_element返回的精确坐标
- **状态验证**: 适当使用check_page_display验证页面状态
- **错误处理**: 工具失败时尝试1-2次，然后继续后续步骤

**执行流程：**
- 从find_available_device开始找到可用设备
- 依次执行每个测试步骤，注意参数的动态替换
- 完成后调用record_agent_summary记录整体执行总结

请用简洁的中文回应，专注于工具调用的执行过程。"""
    
    def chat(self, user_input: str) -> str:
        """处理用户输入并生成响应"""
        try:
            # 设置当前任务的日志管理器，确保工具调用使用同一个日志管理器
            set_current_task_log_manager(self.log_manager)
            
            # 记录用户输入
            self.log_manager.info_agent(f"收到用户输入: {user_input}")
            
            # 添加用户消息到历史
            self.chat_history.append(HumanMessage(content=user_input))
            
            # 执行对话逻辑
            if self.supports_tools:
                response = self._chat_with_tools(user_input)
            else:
                response = self._chat_without_tools(user_input)
            
            return response
                
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            self.log_manager.error_agent(error_msg)
            
            return error_msg
        finally:
            # 确保清理设备状态
            self._cleanup_device_status()
    
    def _chat_with_tools(self, user_input: str) -> str:
        """使用工具调用模式"""
        # 开始任务结构化日志记录
        task_id = self.log_manager.start_task(user_input)
        
        # 构建完整的消息列表
        messages = [
            SystemMessage(content=self._create_system_message()),
            *self.chat_history
        ]
        
        # 计算上下文使用情况
        context_usage = self._calculate_context_usage(messages)
        
        # 记录上下文使用统计
        self.log_manager.info_agent(f"📊 上下文使用统计:")
        self.log_manager.info_agent(f"  总消息数: {context_usage['total_messages']}")
        self.log_manager.info_agent(f"  总字符数: {context_usage['total_characters']:,}")
        self.log_manager.info_agent(f"  估算token数: {context_usage['estimated_total_tokens']:,}")
        self.log_manager.info_agent(f"  使用率: {context_usage['usage_percentage']:.1f}% ({context_usage['estimated_total_tokens']}/{context_usage['context_limit']})")
        
        if context_usage['is_critical']:
            self.log_manager.error_agent("上下文使用率超过95%，可能导致模型遗忘早期信息!")
        elif context_usage['is_approaching_limit']:
            self.log_manager.warning_agent("上下文使用率超过80%，建议注意context长度")
        
        self.log_manager.log_llm_input(messages, "MLX_Omni_Server")
        
        # 支持多轮工具调用的循环
        max_rounds = 50
        round_count = 0
        consecutive_failures = 0
        
        while round_count < max_rounds:
            round_count += 1
            
            # 开始新轮次的结构化日志记录
            current_round = self.log_manager.start_round()
            self.log_manager.info_agent(f"━━━ 开始第 {current_round} 轮执行 ━━━")
            
            # 调用LLM
            self.log_manager.info_agent(f"正在询问模型下一步操作...")
            response = self.llm_with_tools.invoke(messages)
            
            # 记录LLM详细输出
            self.log_manager.log_llm_output(response, "MLX_Omni_Server")
            
            # 检查是否有工具调用
            if hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行第一个工具调用
                tool_call = response.tool_calls[0]
                tool_name = tool_call.get('name', 'unknown')
                
                if len(response.tool_calls) > 1:
                    self.log_manager.info_agent(f"模型请求了 {len(response.tool_calls)} 个工具，但本轮只执行第一个: '{tool_name}'")
                else:
                    self.log_manager.info_agent(f"准备执行工具: '{tool_name}'")
                
                self.log_manager.log_model_decision(tool_calls=response.tool_calls, direct_reply=False)
                
                # 添加AI响应到历史
                self.chat_history.append(response)
                
                # 执行工具调用
                self.log_manager.info_agent(f"正在执行工具 '{tool_name}'...")
                
                tool_start_time = time.time()
                tool_result = self._execute_tool_call(tool_call)
                tool_duration = time.time() - tool_start_time
                
                # 检查工具执行结果
                tool_success = False
                try:
                    result_data = json.loads(tool_result)
                    # 优先检查新的统一状态字段，后备检查旧的status字段
                    execution_status = result_data.get('execution_status', result_data.get('status', 'unknown'))
                    
                    if execution_status in ['success', 'completed']:
                        tool_success = True
                        consecutive_failures = 0
                    elif execution_status in ['error', 'failed']:
                        tool_success = False
                        consecutive_failures += 1
                        self.log_manager.warning_agent(f"工具执行失败，连续失败次数: {consecutive_failures}")
                    else:
                        # 对于未知状态，检查结果内容判断
                        if ('error' in tool_result.lower() or 'failed' in tool_result.lower() or 
                            'exception' in tool_result.lower() or 'traceback' in tool_result.lower()):
                            tool_success = False
                            consecutive_failures += 1
                            self.log_manager.warning_agent(f"工具执行失败（未知状态但含错误信息），连续失败次数: {consecutive_failures}")
                        else:
                            tool_success = True
                            consecutive_failures = 0
                            self.log_manager.info_agent(f"工具执行成功（未知状态但无错误信息）")
                            
                except Exception as parse_error:
                    self.log_manager.warning_agent(f"工具结果JSON解析失败: {parse_error}")
                    # JSON解析失败时，通过字符串内容判断
                    if ('error' in tool_result.lower() or 'failed' in tool_result.lower() or 
                        'exception' in tool_result.lower() or 'traceback' in tool_result.lower()):
                        tool_success = False
                        consecutive_failures += 1
                        self.log_manager.warning_agent(f"工具执行可能失败，连续失败次数: {consecutive_failures}")
                    else:
                        tool_success = True
                        consecutive_failures = 0
                
                # 记录结构化工具执行日志
                self.log_manager.log_tool_execution(
                    tool_name=tool_name,
                    tool_args=tool_call.get('args', {}),
                    tool_result=tool_result,
                    duration=tool_duration
                )
                
                if tool_success:
                    self.log_manager.info_agent(f"✅ 工具 '{tool_name}' 执行成功")
                else:
                    self.log_manager.error_agent(f"❌ 工具 '{tool_name}' 执行失败")
                
                # 记录工具执行结果
                self.log_manager.log_llm_tool_result(tool_name, tool_result, "MLX_Omni_Server")
                self.chat_history.append(
                    ToolMessage(
                        content=tool_result,
                        tool_call_id=tool_call.get('id', 'unknown')
                    )
                )
                
                # 检查是否需要提前终止
                if consecutive_failures >= 3:
                    self.log_manager.error_agent(f"连续失败次数过多 ({consecutive_failures})，可能存在系统性问题")
                    self.log_manager.error_agent("为避免浪费资源，提前终止执行")
                    break
                
                # 更新消息列表，准备下一轮
                messages = [
                    SystemMessage(content=self._create_system_message()),
                    *self.chat_history
                ]
                
                # 继续下一轮循环
                continue
            else:
                # 没有工具调用，智能任务直接认为任务完成
                self.chat_history.append(response)
                
                # 记录最终回复到结构化日志
                self.log_manager.log_final_reply(response.content)
                self.log_manager.log_model_decision(direct_reply=True)
                
                self.log_manager.info_agent(f"🎉 智能任务执行完成！")
                self.log_manager.info_agent(f"📊 总执行轮数: {round_count} 轮")
                
                # 结束任务结构化日志记录
                self.log_manager.end_task(status="成功")
                
                return response.content
        
        # 达到最大轮数限制
        if round_count >= max_rounds:
            self.log_manager.warning_agent(f"达到最大工具调用轮数限制 ({max_rounds})，强制结束")
            termination_reason = "超时"
        elif consecutive_failures >= 3:
            termination_reason = "连续失败"
        else:
            termination_reason = "其他原因"
        
        # 结束任务结构化日志记录
        self.log_manager.end_task(status=termination_reason)
        
        # 返回最后的响应
        if hasattr(response, 'content'):
            self.chat_history.append(response)
            return response.content
        else:
            return f"任务执行终止 ({termination_reason})，请重新开始"
    
    def _chat_without_tools(self, user_input: str) -> str:
        """不使用工具的对话模式"""
        messages = [
            SystemMessage(content="你是一个友好的AI助手，请用中文回答问题。"),
            *self.chat_history
        ]
        
        response = self.llm.invoke(messages)
        self.chat_history.append(response)
        
        return response.content
    
    def _execute_tool_call(self, tool_call: Dict[str, Any]) -> str:
        """执行工具调用"""
        try:
            tool_name = tool_call['name']
            tool_args = tool_call.get('args', {})
            
            self.log_manager.info_agent(f"开始执行工具: {tool_name}, 参数: {tool_args}")
            
            # 查找并执行工具
            for tool in self.tools:
                if tool.name == tool_name:
                    result = tool.func(**tool_args)
                    self.log_manager.info_agent(f"工具 {tool_name} 执行成功")
                    return result
            
            error_msg = f"未找到工具: {tool_name}"
            self.log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "message": error_msg
            }, ensure_ascii=False)
            
        except Exception as e:
            error_msg = f"执行工具 {tool_call.get('name', 'unknown')} 时出错: {str(e)}"
            self.log_manager.error_agent(error_msg)
            return json.dumps({
                "status": "error",
                "error": str(e),
                "message": error_msg
            }, ensure_ascii=False)
    
    def clear_history(self):
        """清除聊天历史"""
        self.chat_history.clear()
        self.log_manager.info_agent("聊天历史已清除")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools]
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_type": "MLX_Omni_Server",
            "base_url": self.base_url,
            "temperature": self.temperature,
            "supports_tools": self.supports_tools,
            "task_id": self.task_id,
            "round_id": self.round_id,
            "round_log_dir": self.round_log_dir,
            "create_round_logs": self.create_round_logs
        }


def list_json_plans() -> List[str]:
    """列出可用的JSON测试计划文件"""
    json_plans_dir = "simplify_agent/log/json_plan"
    if not os.path.exists(json_plans_dir):
        return []
    
    json_files = []
    for file in os.listdir(json_plans_dir):
        if file.startswith("plan_") and file.endswith(".json"):
            json_files.append(os.path.join(json_plans_dir, file))
    
    return sorted(json_files, key=os.path.getmtime, reverse=True)


def interactive_select_plan() -> Optional[str]:
    """交互式选择测试计划"""
    plans = list_json_plans()
    
    if not plans:
        print("❌ 未找到可用的测试计划文件")
        return None
    
    print("\n📋 可用的测试计划文件:")
    for i, plan in enumerate(plans, 1):
        # 获取文件修改时间
        mod_time = time.ctime(os.path.getmtime(plan))
        file_size = os.path.getsize(plan)
        print(f"  {i}. {os.path.basename(plan)} ({file_size} bytes, 修改时间: {mod_time})")
    
    while True:
        try:
            choice = input(f"\n请选择测试计划 (1-{len(plans)}) 或输入 'q' 退出: ").strip()
            
            if choice.lower() == 'q':
                return None
            
            choice_num = int(choice)
            if 1 <= choice_num <= len(plans):
                selected_plan = plans[choice_num - 1]
                print(f"✅ 已选择: {os.path.basename(selected_plan)}")
                return selected_plan
            else:
                print(f"❌ 请输入 1-{len(plans)} 之间的数字")
        except ValueError:
            print("❌ 请输入有效的数字")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="独立执行的移动设备测试 Agent")
    parser.add_argument("json_file", nargs="?", help="JSON 测试计划文件路径")
    parser.add_argument("--base-url", default="http://127.0.0.1:10240", help="MLX-Omni-Server 服务器地址")
    parser.add_argument("--temperature", type=float, default=0.1, help="模型温度参数")
    parser.add_argument("--no-round-logs", action="store_true", help="禁用轮次日志目录创建")
    parser.add_argument("--no-auto-server", action="store_true", help="禁用自动启动/停止 MLX-Omni-Server")
    args = parser.parse_args()
    
    print("🤖 独立执行的移动设备测试 Agent")
    print("=" * 50)
    
    # 设置信号处理器确保资源清理
    agent = None
    
    def signal_handler(signum, frame):
        print(f"\n⚠️ 接收到中断信号 ({signum})，正在清理资源...")
        if agent:
            agent.cleanup()
        print("✅ 资源清理完成，程序退出")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 在某些系统上也注册其他信号
    try:
        signal.signal(signal.SIGHUP, signal_handler)
        signal.signal(signal.SIGQUIT, signal_handler)
    except AttributeError:
        # Windows 系统可能没有这些信号
        pass
    
    # 确定要执行的测试计划
    json_file_path = args.json_file
    
    if not json_file_path:
        json_file_path = interactive_select_plan()
        if not json_file_path:
            print("👋 程序退出")
            return
    
    if not os.path.exists(json_file_path):
        print(f"❌ 文件不存在: {json_file_path}")
        return
    
    try:
        # 创建 Agent
        print(f"\n🚀 初始化 Agent...")
        print(f"  MLX-Omni-Server 服务器: {args.base_url}")
        print(f"  模型温度: {args.temperature}")
        
        task_id = f"agent_alone_{int(time.time())}"
        create_round_logs = not args.no_round_logs  # 默认创建轮次日志，除非指定禁用
        auto_manage_server = not args.no_auto_server  # 默认自动管理服务器，除非指定禁用
        
        agent = AgentAlone(
            base_url=args.base_url,
            temperature=args.temperature,
            task_id=task_id,
            create_round_logs=create_round_logs,
            auto_manage_server=auto_manage_server
        )
        
        # 获取模型信息
        model_info = agent.get_model_info()
        print(f"  工具支持: {'是' if model_info['supports_tools'] else '否'}")
        print(f"  可用工具数量: {len(agent.get_available_tools())}")
        print(f"  自动服务器管理: {'是' if auto_manage_server else '否'}")
        
        # 显示轮次信息
        if model_info.get('create_round_logs'):
            print(f"  轮次ID: {model_info.get('round_id')}")
            print(f"  日志目录: {model_info.get('round_log_dir')}")
        else:
            print("  日志模式: 全局日志")
        
        print(f"\n📂 加载测试计划: {os.path.basename(json_file_path)}")
        
        # 执行测试计划
        result = agent.execute_test_plan(json_file_path)
        
        print(f"\n🎉 测试执行完成!")
        print("=" * 50)
        print("📊 执行结果摘要:")
        print(result[:500] + "..." if len(result) > 500 else result)
        
        # 显示日志位置
        if model_info.get('create_round_logs') and model_info.get('round_log_dir'):
            print(f"\n📁 详细日志位置: {model_info.get('round_log_dir')}")
            print("   包含文件: agent.log, tools.log, task_structured.log, unified.log")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {str(e)}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")


if __name__ == "__main__":
    main()