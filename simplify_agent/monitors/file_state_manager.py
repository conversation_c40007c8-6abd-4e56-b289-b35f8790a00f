#!/usr/bin/env python3
"""
SimplifyAgent 简化文件状态管理器

简化的文件状态管理器，只在内存中记录启动时存在的文件，
避免重复处理历史文件。不使用持久化存储。

作者: SimplifyAgent Development Team  
创建时间: 2024-09-05
修改时间: 2024-09-05 - 简化架构
"""

import os
from pathlib import Path
from typing import Dict, Set
import threading

from server_logger import get_logger


class SimplifiedFileStateManager:
    """简化的文件状态管理器"""
    
    def __init__(self, config: 'LogServerConfig'):
        """初始化简化的文件状态管理器"""
        self.config = config
        self.logger = get_logger('log_server', 'file_state_manager')
        
        # 启动时存在的文件记录（只在内存中）
        self.startup_files = {
            'json_plan': set(),      # 启动时存在的JSON文件名
            'agent_execute': set(),  # 启动时存在的执行文件夹名
            'judge_report': set()    # 启动时存在的报告文件名
        }
        
        # 线程锁
        self.state_lock = threading.Lock()
        
        # 监控路径
        self.watch_paths = config.get_watch_paths()
        
        self.logger.info("简化文件状态管理器初始化完成")
    
    
    def record_startup_files(self):
        """记录启动时存在的文件（启动时调用）"""
        self.logger.info("开始记录启动时存在的文件...")
        
        recorded_count = 0
        
        with self.state_lock:
            # 记录JSON计划文件
            json_plan_path = Path(self.watch_paths['json_plan'])
            if json_plan_path.exists():
                for json_file in json_plan_path.glob('*.json'):
                    if not json_file.name.startswith('.'):
                        self.startup_files['json_plan'].add(json_file.name)
                        recorded_count += 1
            
            # 记录执行日志文件夹
            agent_execute_path = Path(self.watch_paths['agent_execute'])
            if agent_execute_path.exists():
                for folder in agent_execute_path.iterdir():
                    if folder.is_dir() and folder.name.startswith('round_'):
                        self.startup_files['agent_execute'].add(folder.name)
                        recorded_count += 1
            
            # 记录评价报告文件
            judge_report_path = Path(self.watch_paths['judge_report'])
            if judge_report_path.exists():
                for report_file in judge_report_path.glob('*_report.json'):
                    self.startup_files['judge_report'].add(report_file.name)
                    recorded_count += 1
        
        self.logger.info(f"记录完成: {recorded_count} 个启动时存在的文件已记录到内存中")
    
    def is_new_file(self, file_path: str, watch_type: str) -> bool:
        """检查文件是否为新文件（启动后新增的文件）"""
        file_name = os.path.basename(file_path)
        
        with self.state_lock:
            # 简单判断：不在启动时记录的文件列表中就是新文件
            return file_name not in self.startup_files.get(watch_type, set())
    
    def mark_file_processed(self, file_path: str, watch_type: str, success: bool = True):
        """标记文件为已处理（简化版，主要用于日志记录）"""
        file_name = os.path.basename(file_path)
        
        # 简化版：只记录日志，不维护复杂状态
        self.logger.debug(f"文件处理完成: {file_name} ({watch_type}) - {'成功' if success else '失败'}")
    
    def cleanup_deleted_files(self):
        """清理已删除文件的状态记录（简化版，无需操作）"""
        # 简化版：由于不维护持久化状态，无需清理操作
        self.logger.debug("简化版状态管理器：无需清理已删除文件记录")
    
    def get_statistics(self) -> Dict[str, int]:
        """获取状态统计信息"""
        with self.state_lock:
            stats = {}
            total_startup_files = 0
            
            for watch_type, file_set in self.startup_files.items():
                file_count = len(file_set)
                stats[watch_type] = {
                    'startup_files_count': file_count
                }
                total_startup_files += file_count
            
            stats['overall'] = {
                'total_startup_files': total_startup_files
            }
            
            return stats


# 为了向后兼容，保留原类名的别名
FileStateManager = SimplifiedFileStateManager


if __name__ == '__main__':
    # 简化状态管理器测试
    import sys
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from config import get_config
    
    config = get_config()
    state_manager = SimplifiedFileStateManager(config)
    
    # 记录启动时文件
    state_manager.record_startup_files()
    
    # 显示统计信息
    stats = state_manager.get_statistics()
    print(f"简化文件状态统计: {stats}")