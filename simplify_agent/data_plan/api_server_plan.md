# SimplifyAgent API服务器开发计划

## 项目概述
构建统一的API服务器来串联现有的三个MLX脚本，实现完整的自动化测试流程，支持多进程并发执行。

## 目标
- 提供REST API接口接收测试指令
- 自动化执行：计划生成 → 测试执行 → 结果评价
- 支持多进程并发测试（至少2个进程同时运行）
- 完整的任务状态追踪和结果查询
- 与现有数据库系统集成
- 提供实时监控和日志管理
- **保持原脚本独立可执行性** - 封装过程不影响单独测试和调试能力

---

## 现有脚本分析

### 核心模块总览
- **测试计划生成器**: `agent_json_planer_mlx_without_rag.py`
- **测试执行器**: `agent_mlx_omni_without_rag.py` 
- **测试评价器**: `agent_mlx_process_judge_without_rag.py`

### 模块特性分析

#### 3.1 测试计划生成器
**技术特点**:
- MLX框架本地推理，避免网络依赖
- 线程安全的模型锁机制（`_model_lock`）
- 平台轮询机制（iOS/Android自动切换）
- 流式生成，支持实时停止检测

**关键接口**:
- `generate_plan(natural_language_request, platform, debug_mode)`
- `determine_platform(natural_language, specified_platform)`
- `convert_plan_to_detailed_instruction(plan)`

**资源要求**:
- 独占MLX模型加载（约15秒启动时间）
- 内存占用：约4-8GB（取决于模型大小）
- CPU密集型任务

#### 3.2 测试执行器  
**技术特点**:
- 自动管理MLX-Omni-Server进程（启动/停止）
- 支持17种测试工具（设备操作、OCR、UI分析等）
- 多轮工具调用（最多50轮）
- 完整的轮次日志系统

**关键接口**:
- `execute_test_plan(json_file_path)`
- `load_test_plan(json_file_path)`
- `chat(user_input)`

**资源要求**:
- MLX-Omni-Server端口占用（默认10240）
- 设备资源管理（需要独占测试设备）
- 日志目录管理（轮次隔离）

#### 3.3 测试评价器
**技术特点**:
- 解析`task_structured.log`日志文件
- 多维度评分系统（计划质量、执行符合度等）
- 支持结构化JSON评价输出
- 自动保存评价报告到`judge_report`目录

**关键接口**:
- `judge_test_execution(round_folder_path)`
- `parse_test_log(log_file_path)`
- `_build_analysis_prompt(log_data)`

**资源要求**:
- 共享MLX模型访问（可与生成器共用）
- 文件I/O密集型（日志解析）

---

## API服务器架构设计

### 整体架构概览
```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │  REST API   │ │  WebSocket  │ │  Health & Monitoring    │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Master Process                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Task      │ │  Resource   │ │    Database             │ │
│  │  Scheduler  │ │  Manager    │ │   Integration           │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Worker Processes                        │
│  ┌─────────────────────────┐  ┌─────────────────────────────┐│
│  │       Worker-1          │  │        Worker-2             ││
│  │  ┌─────┐ ┌─────┐ ┌─────┐│  │  ┌─────┐ ┌─────┐ ┌─────┐   ││
│  │  │Plan │ │Exec │ │Judge││  │  │Plan │ │Exec │ │Judge│   ││
│  │  │Gen  │ │utor │ │ment ││  │  │Gen  │ │utor │ │ment │   ││
│  │  └─────┘ └─────┘ └─────┘│  │  └─────┘ └─────┘ └─────┘   ││
│  │  Port: 10240            │  │  Port: 10241                ││
│  └─────────────────────────┘  └─────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

### 核心组件设计

#### 4.1 Master Process（主进程）
**职责**:
- API服务（Flask/FastAPI）
- 任务调度和队列管理
- Worker进程生命周期管理
- 资源分配（端口、设备、日志目录）
- 数据库操作和状态同步

**技术架构**:
- Web框架：FastAPI（异步支持，自动API文档）
- 任务队列：Redis + Celery 或 Python multiprocessing.Queue
- 进程管理：multiprocessing.Process
- 数据库：集成现有SQLite数据库系统

#### 4.2 Worker Process（工作进程）
**职责**:
- 执行完整的三阶段任务流水线
- 独立的资源隔离（端口、日志、临时文件）
- 状态报告给Master进程
- 异常处理和恢复

**资源分配策略**:
- **端口分配**: 10240 + worker_id（10240, 10241, 10242...）
- **日志目录**: `log/api_server/worker_{worker_id}/task_{task_id}/`
- **MLX模型**: Worker间共享（通过文件锁协调）
- **设备资源**: 动态分配，互斥锁保护

---

## 开发计划

### 阶段一：基础架构搭建 [ ] 未完成

#### 1.1 项目结构建立 [ ] 未完成
**文件**: `simplify_agent/api_server/`
```
simplify_agent/api_server/
├── __init__.py
├── main.py                 # FastAPI应用入口
├── config/
│   ├── __init__.py
│   ├── settings.py        # 配置管理
│   └── logging_config.py  # 日志配置
├── core/
│   ├── __init__.py
│   ├── master.py          # Master进程主逻辑
│   ├── worker.py          # Worker进程实现
│   ├── task_queue.py      # 任务队列管理
│   └── resource_manager.py # 资源分配管理
├── api/
│   ├── __init__.py
│   ├── routes.py          # API路由定义
│   ├── models.py          # Pydantic数据模型
│   └── dependencies.py    # 依赖注入
├── services/
│   ├── __init__.py
│   ├── task_service.py    # 任务业务逻辑
│   ├── monitoring_service.py # 监控服务
│   └── database_service.py   # 数据库服务封装
└── utils/
    ├── __init__.py
    ├── exceptions.py      # 异常定义
    └── helpers.py         # 工具函数
```

#### 1.2 配置系统设计 [ ] 未完成
**文件**: `simplify_agent/api_server/config/settings.py`
**功能**:
- 支持环境变量和配置文件
- Worker进程数量配置
- 端口范围配置  
- MLX模型路径配置
- 数据库连接配置
- 日志级别和路径配置

**关键配置项**:
```python
# 服务器配置
HOST = "0.0.0.0"
PORT = 8000
DEBUG = False

# Worker配置
MAX_WORKERS = 4
MIN_WORKERS = 2
MLX_SERVER_PORT_START = 10240

# MLX模型配置
PLANNER_MODEL_PATH = "/Users/<USER>/Desktop/work/ai_models/..."
JUDGE_MODEL_PATH = "/Users/<USER>/Desktop/work/ai_models/..."

# 数据库配置
DATABASE_PATH = "simplify_agent/data/simplify_agent.db"

# 日志配置
LOG_DIR = "simplify_agent/log/api_server"
```

#### 1.3 基础API框架 [ ] 未完成
**文件**: `simplify_agent/api_server/main.py`
**功能**:
- FastAPI应用初始化
- 中间件配置（CORS、日志、异常处理）
- API路由注册
- 应用生命周期管理

---

### 阶段二：核心服务实现 [ ] 未完成

#### 2.1 Master进程实现 [ ] 未完成

##### 2.1.1 任务队列管理 [ ] 未完成
**文件**: `simplify_agent/api_server/core/task_queue.py`
**功能**:
- 任务提交和优先级管理
- 队列状态监控
- 任务超时处理
- 重试机制

**关键接口**:
```python
class TaskQueue:
    def submit_task(self, task: TestTask) -> str  # 提交任务，返回task_id
    def get_task_status(self, task_id: str) -> TaskStatus
    def cancel_task(self, task_id: str) -> bool
    def get_queue_info(self) -> QueueInfo
```

##### 2.1.2 资源管理器 [ ] 未完成
**文件**: `simplify_agent/api_server/core/resource_manager.py`
**功能**:
- Worker进程池管理
- 端口动态分配
- 设备资源锁定
- 资源使用统计

**关键接口**:
```python
class ResourceManager:
    def allocate_worker(self) -> Optional[WorkerInfo]
    def release_worker(self, worker_id: str) -> None
    def get_available_devices(self, platform: str) -> List[str]
    def lock_device(self, udid: str, worker_id: str) -> bool
```

##### 2.1.3 Master进程主控制器 [ ] 未完成
**文件**: `simplify_agent/api_server/core/master.py`
**功能**:
- 整体进程协调
- Worker进程生命周期管理
- 异常监控和恢复
- 优雅关闭处理

#### 2.2 Worker进程实现 [ ] 未完成

##### 2.2.1 Worker进程核心逻辑 [ ] 未完成
**文件**: `simplify_agent/api_server/core/worker.py`
**功能**:
- 三阶段任务执行流水线
- 现有脚本集成封装
- 资源隔离和清理
- 状态报告和错误处理

**执行流程**:
```python
class TestWorker:
    def execute_task(self, task: TestTask) -> TaskResult:
        # 1. 生成测试计划
        plan_result = self.generate_plan(task.instruction, task.platform)
        
        # 2. 执行测试计划
        execution_result = self.execute_plan(plan_result.plan_file)
        
        # 3. 评价测试结果
        evaluation_result = self.judge_execution(execution_result.round_folder)
        
        return TaskResult(plan=plan_result, 
                         execution=execution_result,
                         evaluation=evaluation_result)
```

##### 2.2.2 脚本集成封装 [ ] 未完成
**文件**: `simplify_agent/api_server/integrations/`
- `planner_wrapper.py` - 封装测试计划生成器
- `executor_wrapper.py` - 封装测试执行器  
- `judge_wrapper.py` - 封装测试评价器

**封装策略**:
- **非侵入式封装**: 不修改原脚本，通过装饰器和适配器模式封装
- **保持原有接口**: 原始的 `if __name__ == "__main__"` 逻辑完全保留
- **双重访问模式**: 既可独立运行，也可作为模块导入
- **配置兼容性**: API配置优先，原脚本配置作为后备
- **日志隔离**: API模式和独立模式使用不同的日志配置

**具体实现方案**:
```python
# planner_wrapper.py 示例
from agent_json_planer_mlx_without_rag import AgentJsonPlanerMLX

class PlannerService:
    def __init__(self, api_config=None):
        # 使用API配置或默认配置
        model_path = api_config.get('model_path') if api_config else DEFAULT_MODEL_PATH
        self.planner = AgentJsonPlanerMLX(
            model_path=model_path,
            max_tokens=api_config.get('max_tokens', DEFAULT_MAX_TOKENS) if api_config else DEFAULT_MAX_TOKENS,
            enable_logs=False  # API模式禁用原始日志，使用统一日志系统
        )
    
    def generate_plan(self, instruction: str, platform: str = None) -> dict:
        """API友好的接口封装"""
        try:
            result = self.planner.generate_plan(instruction, platform)
            return self._normalize_result(result)
        except Exception as e:
            # 统一异常处理
            raise PlannerException(f"计划生成失败: {str(e)}")

# 原脚本保持完全不变，可以独立运行
# python agent_json_planer_mlx_without_rag.py 
```

**关键设计原则**:
- **零修改原则**: 原脚本文件不做任何修改
- **兼容性保证**: 原有的命令行参数和功能完全保持
- **资源共享**: API模式下的资源管理不影响独立模式
- **配置隔离**: 两种模式使用独立的配置和日志系统

---

### 阶段三：API接口实现 [ ] 未完成

#### 3.1 核心API接口 [ ] 未完成

##### 3.1.1 任务提交接口 [ ] 未完成
```python
@app.post("/api/v1/tasks/submit")
async def submit_test_task(request: TestTaskRequest) -> TestTaskResponse:
    """
    提交测试任务
    
    请求体:
    {
        "instruction": "测试美团APP首页功能",
        "platform": "ios",  # 可选，不指定则自动选择
        "priority": 1,      # 可选，优先级
        "callback_url": "http://..." # 可选，完成回调
    }
    
    响应:
    {
        "task_id": "task_20250906_001",
        "status": "queued",
        "estimated_wait_time": 120,
        "message": "任务已提交，预计等待时间2分钟"
    }
    """
```

##### 3.1.2 任务状态查询接口 [ ] 未完成
```python
@app.get("/api/v1/tasks/{task_id}/status")
async def get_task_status(task_id: str) -> TaskStatusResponse:
    """
    查询任务状态
    
    响应:
    {
        "task_id": "task_20250906_001",
        "status": "running",  # queued, running, completed, failed, cancelled
        "current_stage": "execution",  # planning, execution, evaluation
        "progress": 65,       # 进度百分比
        "start_time": "2025-09-06T10:30:00",
        "estimated_completion": "2025-09-06T10:45:00",
        "worker_id": "worker_1",
        "message": "正在执行第15步：点击搜索按钮"
    }
    """
```

##### 3.1.3 任务结果获取接口 [ ] 未完成
```python
@app.get("/api/v1/tasks/{task_id}/result")
async def get_task_result(task_id: str, include_logs: bool = False) -> TaskResultResponse:
    """
    获取任务执行结果
    
    响应:
    {
        "task_id": "task_20250906_001",
        "status": "completed",
        "execution_summary": {
            "total_steps": 12,
            "successful_steps": 11,
            "failed_steps": 1,
            "total_duration": "3分42秒"
        },
        "evaluation": {
            "overall_success": true,
            "success_score": 0.92,
            "key_findings": ["UI响应正常", "功能测试通过"],
            "improvement_suggestions": ["建议增加异常场景测试"]
        },
        "report_files": {
            "detailed_log": "/api/v1/tasks/task_001/logs/detailed",
            "evaluation_report": "/api/v1/tasks/task_001/evaluation",
            "screenshots": "/api/v1/tasks/task_001/screenshots"
        }
    }
    """
```

#### 3.2 监控和管理接口 [ ] 未完成

##### 3.2.1 系统状态接口 [ ] 未完成
```python
@app.get("/api/v1/system/status")
async def get_system_status() -> SystemStatusResponse:
    """
    系统状态查询
    
    响应:
    {
        "server_status": "running",
        "workers": {
            "total": 4,
            "active": 2,
            "idle": 2,
            "details": [
                {
                    "worker_id": "worker_1",
                    "status": "busy",
                    "current_task": "task_001",
                    "mlx_server_port": 10240
                }
            ]
        },
        "queue": {
            "pending": 3,
            "completed_today": 25,
            "average_duration": "4分15秒"
        },
        "devices": {
            "ios_available": 2,
            "android_available": 3,
            "total_in_use": 1
        }
    }
    """
```

##### 3.2.2 任务管理接口 [ ] 未完成
```python
@app.delete("/api/v1/tasks/{task_id}")
async def cancel_task(task_id: str) -> CancelTaskResponse

@app.get("/api/v1/tasks")
async def list_tasks(status: str = None, limit: int = 50) -> TaskListResponse

@app.post("/api/v1/tasks/{task_id}/retry")
async def retry_task(task_id: str) -> RetryTaskResponse
```

#### 3.3 WebSocket实时更新 [ ] 未完成
```python
@app.websocket("/api/v1/tasks/{task_id}/ws")
async def task_websocket(websocket: WebSocket, task_id: str):
    """
    任务实时状态推送
    
    推送消息格式:
    {
        "type": "status_update",
        "task_id": "task_001",
        "status": "running",
        "stage": "execution",
        "step": "点击搜索按钮",
        "progress": 65,
        "timestamp": "2025-09-06T10:30:00"
    }
    """
```

---

### 阶段四：数据库集成 [ ] 未完成

#### 4.1 数据库服务集成 [ ] 未完成
**文件**: `simplify_agent/api_server/services/database_service.py`
**功能**:
- 集成现有的DAO系统
- 任务数据自动存储
- 执行结果持久化
- 统计数据查询

**关键接口**:
```python
class DatabaseService:
    def save_task_plan(self, task_id: str, plan_data: dict) -> None
    def save_execution_result(self, task_id: str, execution_data: dict) -> None
    def save_evaluation_result(self, task_id: str, evaluation_data: dict) -> None
    def get_task_statistics(self, days: int = 30) -> TaskStatistics
```

#### 4.2 数据自动采集 [ ] 未完成
**功能**:
- Worker执行过程中自动保存数据
- 日志文件解析和结构化存储
- 失败任务详情记录
- 性能指标采集

---

### 阶段五：部署和监控 [ ] 未完成

#### 5.1 容器化部署 [ ] 未完成
**文件**: `Dockerfile`, `docker-compose.yml`
**功能**:
- Docker镜像构建
- 多容器编排
- 环境变量管理
- 数据卷配置

#### 5.2 监控和告警 [ ] 未完成
**功能**:
- Prometheus指标采集
- Grafana仪表板
- 异常告警通知
- 性能趋势分析

#### 5.3 日志管理 [ ] 未完成
**功能**:
- 集中化日志收集
- 日志轮转和清理
- 结构化日志存储
- 错误日志聚合分析

---

## 技术规范

### 并发控制策略

#### Worker进程管理
- **最大并发数**: 可配置（默认4个Worker）
- **进程复用**: Worker进程长期运行，任务完成后不退出
- **健康检查**: 定期检测Worker进程状态
- **异常恢复**: Worker异常退出时自动重启

#### 资源竞争处理
- **MLX模型**: 通过文件锁协调多Worker共享
- **设备资源**: 数据库级别的设备锁定机制
- **端口分配**: 预分配固定端口段，避免冲突
- **日志隔离**: 每个任务独立日志目录

### 性能优化策略

#### 模型加载优化
- **预热机制**: 服务启动时预加载MLX模型
- **模型共享**: Worker间共享已加载的模型实例
- **内存管理**: 定期清理不必要的模型缓存

#### 任务调度优化
- **智能分配**: 根据Worker负载和设备可用性分配任务
- **批处理**: 相同平台的任务优先分配给同一Worker
- **优先级队列**: 支持任务优先级和快速通道

---

## 配置文件示例

### API服务器配置
**文件**: `simplify_agent/api_server/config/api_server.yaml`
```yaml
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1  # FastAPI主进程worker数
  reload: false
  
worker:
  max_workers: 4
  min_workers: 2
  auto_scale: true
  idle_timeout: 300  # 空闲超时时间（秒）
  
mlx:
  planner_model: "/Users/<USER>/Desktop/work/ai_models/lm_studio/mlx-community/DeepSeek-R1-Distill-Llama-70B-4bit"
  judge_model: "/Users/<USER>/Desktop/work/ai_models/lm_studio/Qwen/Qwen3-Coder-30B-A3B-Instruct-MLX-4bit"
  server_port_start: 10240
  max_tokens: 20000
  
database:
  path: "simplify_agent/data/simplify_agent.db"
  connection_pool_size: 10
  
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "simplify_agent/log/api_server/server.log"
  max_size: "100MB"
  backup_count: 5
  
monitoring:
  enable_metrics: true
  metrics_port: 9090
  health_check_interval: 30
```

---

## 开发约定

### 代码规范
- **Python版本**: >= 3.8
- **类型提示**: 使用完整的类型标注
- **文档字符串**: 使用Google风格docstring
- **异常处理**: 详细的异常信息和恢复策略
- **日志记录**: 结构化日志，包含请求ID追踪

### 脚本独立性约定
- **零修改原则**: 绝对不修改现有三个脚本的源码
- **兼容性测试**: 每次封装后必须验证原脚本独立运行正常
- **配置隔离**: API配置和原脚本配置完全分离
- **依赖独立**: 新增的API依赖不能影响原脚本运行
- **测试保证**: 持续集成中包含原脚本的独立运行测试

### 封装实现规范
```python
# ✅ 正确的封装方式 - 通过导入使用
from agent_json_planer_mlx_without_rag import AgentJsonPlanerMLX

class PlannerService:
    def __init__(self, config):
        self.planner = AgentJsonPlanerMLX(config.model_path)
    
    def generate(self, text):
        return self.planner.generate_plan(text)

# ❌ 错误的方式 - 修改原脚本
# 不允许在原脚本中添加任何API相关代码
```

### 独立测试保证
```bash
# 这些命令必须始终正常工作
python agent_json_planer_mlx_without_rag.py --debug
python agent_mlx_omni_without_rag.py plan_001.json  
python agent_mlx_process_judge_without_rag.py round_001/
```

### API规范
- **RESTful设计**: 遵循REST API设计原则
- **状态码**: 标准HTTP状态码使用
- **响应格式**: 统一的JSON响应格式
- **错误处理**: 友好的错误信息和错误码
- **版本管理**: URL版本化（/api/v1/）

### 数据库规范
- **事务管理**: 关键操作使用事务保护
- **连接池**: 复用数据库连接
- **索引优化**: 为查询字段添加适当索引
- **数据清理**: 定期清理过期数据

---

## 测试策略

### 单元测试 [ ] 未完成
**覆盖范围**:
- 所有核心业务逻辑
- API接口输入输出验证
- 数据库操作正确性
- 异常处理场景

### 集成测试 [ ] 未完成  
**测试场景**:
- 完整任务执行流程
- 多Worker并发测试
- 资源竞争处理
- 异常恢复能力

### 性能测试 [ ] 未完成
**测试指标**:
- API响应时间
- 并发处理能力
- 内存使用情况
- Worker进程稳定性

### 压力测试 [ ] 未完成
**测试场景**:
- 高并发任务提交
- 长时间运行稳定性
- 资源耗尽恢复
- 极端异常情况

---

## 进度跟踪

### 完成标记说明
- [x] 已完成
- [ ] 未完成  
- [~] 进行中
- [!] 需要注意或修改

### 里程碑计划
- **第1周**: 阶段一 - 基础架构搭建完成
- **第2-3周**: 阶段二 - 核心服务实现完成
- **第4周**: 阶段三 - API接口实现完成
- **第5周**: 阶段四 - 数据库集成完成
- **第6周**: 阶段五 - 部署测试完成

### 更新日志
- **2025-09-06**: 初始计划创建，完成架构设计和详细规划

---

## 风险评估

### 技术风险
- **MLX模型兼容性**: 不同版本MLX框架兼容问题
- **进程间通信**: Worker进程异常导致通信中断
- **资源竞争**: 多进程访问同一设备可能冲突
- **内存泄漏**: 长期运行的Worker进程内存管理

### 性能风险
- **模型加载时间**: MLX模型加载可能影响响应速度
- **并发瓶颈**: Worker数量增加时的系统负载
- **数据库压力**: 大量并发写入可能影响性能

### 运维风险
- **配置复杂性**: 多组件配置管理复杂
- **故障排查**: 分布式系统故障定位困难
- **扩容困难**: Worker扩容需要端口和设备资源配套

---

## 后续扩展计划

### 高可用性 [ ] 未来计划
- Master进程高可用
- Worker进程自动故障转移
- 任务状态持久化
- 集群部署支持

### 增强功能 [ ] 未来计划
- 任务模板系统
- 批量任务提交
- 定时任务调度
- 测试报告生成器

### 集成扩展 [ ] 未来计划
- CI/CD流水线集成
- 第三方测试平台对接
- 消息通知系统
- 多租户支持

---

*最后更新: 2025-09-06*  
*当前阶段: 阶段一 - 基础架构搭建 [待开始]*

---

## 立即可开始的开发任务

基于现有代码基础和独立性要求，以下任务可以立即开始：

### 🚀 快速启动任务

#### 1. 验证原脚本独立运行 [ ] 优先级：最高
```bash
# 确保这些命令正常工作（作为基线）
cd simplify_agent/
python agent_json_planer_mlx_without_rag.py --debug
python agent_mlx_omni_without_rag.py  # 使用交互选择
python agent_mlx_process_judge_without_rag.py round_xxx/
```

#### 2. 创建非侵入式封装 [ ] 优先级：高
**目标**: 创建API适配层，不修改原脚本
```python
# simplify_agent/api_server/integrations/planner_service.py
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from agent_json_planer_mlx_without_rag import AgentJsonPlanerMLX

class PlannerService:
    """测试计划生成服务 - 原脚本的API适配器"""
    
    def __init__(self, api_config=None):
        # 使用API配置覆盖默认配置
        self.planner = AgentJsonPlanerMLX(
            model_path=api_config.get('planner_model') if api_config else None,
            enable_logs=False  # API模式下禁用原始文件日志
        )
    
    def generate_plan_for_api(self, instruction: str, platform: str = None) -> dict:
        """API友好的计划生成接口"""
        result = self.planner.generate_plan(instruction, platform)
        return self._standardize_output(result)
```

#### 3. 创建基础项目结构 [ ] 优先级：中
```
simplify_agent/api_server/
├── __init__.py
├── main.py                    # FastAPI入口
├── config/
│   └── settings.py           # 配置管理
├── integrations/             # 原脚本封装层
│   ├── planner_service.py    # 计划生成器封装
│   ├── executor_service.py   # 执行器封装
│   └── judge_service.py      # 评价器封装
└── core/
    └── worker.py             # Worker进程实现
```

#### 4. 实现MVP版本 [ ] 优先级：中
- 单Worker进程API服务器
- 基础的任务提交和查询接口
- 简单的内存队列管理

### 🧪 持续验证策略
```bash
# 每次开发后运行验证脚本
./scripts/test_independence.sh  # 验证原脚本独立性
./scripts/test_api_integration.sh  # 验证API集成
```

### 📝 开发检查清单
- [ ] 原脚本独立运行测试通过
- [ ] 封装后原脚本仍可独立运行
- [ ] API模式和独立模式配置隔离
- [ ] 无新的依赖影响原脚本运行
- [ ] 日志系统不冲突

这种方式确保了你随时可以回到独立模式进行调试和测试，同时逐步构建API服务能力。