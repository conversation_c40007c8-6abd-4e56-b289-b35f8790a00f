#!/usr/bin/env python3
"""
自然语言到结构化执行计划的转换器 (MLX版本)
用于将用户的自然语言测试用例转换为可执行的结构化JSON计划
使用 MLX 框架进行本地推理，避免网络依赖
"""

import json
import re
import threading
import time
import os
import sys
import multiprocessing
from datetime import datetime
from typing import Dict, List, Any, Optional

# 设置多进程启动方法为spawn，避免fork相关问题
try:
    multiprocessing.set_start_method('spawn', force=True)
except RuntimeError:
    # 如果已经设置过，就忽略
    pass

# 导入 MLX 相关库
try:
    import mlx_lm
    MLX_AVAILABLE = True
    print("✅ MLX 框架加载成功")
except ImportError as e:
    print(f"❌ MLX 框架未安装: {e}")
    print("请安装: pip install mlx-lm")
    MLX_AVAILABLE = False

from pydantic import BaseModel

# 默认配置常量 - 统一管理所有默认参数
# 🔧 只需要修改这里的值，就会同时影响 __init__ 和 main 函数的默认参数
DEFAULT_MODEL_PATH = "/Users/<USER>/Desktop/work/ai_models/lm_studio/Qwen/Qwen3-Coder-30B-A3B-Instruct-MLX-4bit"
DEFAULT_MAX_TOKENS = 20000
DEFAULT_ENABLE_LOGS = True

# 添加父目录到路径，支持直接运行和模块导入两种方式
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# 无需RAG系统导入

# 日志管理器导入
try:
    from log_manager import LogManager
    LOG_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 日志管理器不可用: {e}")
    LOG_MANAGER_AVAILABLE = False


class PlanStep(BaseModel):
    """执行步骤定义"""
    step_id: int
    action: str  # 动作类型：find_available_device, take_screenshot, tap_device, slide_device, check_page_display, input_text_smart, wait_seconds等
    description: str  # 步骤描述
    parameters: Dict[str, Any]  # 具体参数
    parameter_types: Dict[str, str] = {}  # 参数类型标识：static(固定值) 或 dynamic(动态值)
    parameter_sources: Dict[str, str] = {}  # 动态参数的来源说明
    expected_result: str  # 预期结果
    

class ExecutionPlan(BaseModel):
    """完整的执行计划"""
    plan_id: str
    original_request: str  # 原始自然语言请求
    summary: str  # 计划摘要
    platform: str  # 平台：ios 或 android
    total_steps: int
    steps: List[PlanStep]


class AgentJsonPlanerMLX:
    """Agent JSON 计划生成器 (MLX版本)"""
    
    # 类级别的线程锁，确保只有一个请求能同时使用模型
    _model_lock = threading.Lock()
    # 平台轮询状态文件路径
    _platform_state_file = ".platform_state.json"
    _platform_lock = threading.Lock()
    
    def __init__(self, 
                 model_path: str = DEFAULT_MODEL_PATH,
                 max_tokens: int = DEFAULT_MAX_TOKENS,  # 设置更大的值确保输出完整
                 enable_logs: bool = DEFAULT_ENABLE_LOGS):
        """初始化计划生成器"""
        
        if not MLX_AVAILABLE:
            raise RuntimeError("MLX框架未安装，请运行: pip install mlx-lm")
            
        self.model_path = model_path
        self.max_tokens = max_tokens
        self.enable_rag = False  # 禁用RAG系统
        self.enable_logs = enable_logs and LOG_MANAGER_AVAILABLE
        
        # 初始化日志管理器
        self.log_manager = None
        if self.enable_logs:
            try:
                # 创建专门的JSON计划日志管理器，使用 simplify_agent/log/json_plan 目录
                log_dir = os.path.join(current_dir, "log", "json_plan")
                self.log_manager = LogManager(log_dir=log_dir, enable_default_logs=False)
                print("✅ 日志管理器初始化成功")
                print(f"📁 日志目录: {log_dir}")
            except Exception as e:
                print(f"⚠️ 日志管理器初始化失败: {e}")
                self.enable_logs = False
        
        # 初始化MLX模型
        self._load_mlx_model()
        
        # 工具定义映射
        self.tool_mapping = self._create_tool_mapping()
        
        # 加载prompt模板
        self.prompt_templates = self._load_prompt_templates()
        
        # RAG系统已禁用
        self.db_manager = None
        self.plan_optimizer = None
    
    def _load_mlx_model(self):
        """加载MLX模型"""
        try:
            print(f"🔧 加载MLX模型: {self.model_path}")
            
            # 检查模型路径是否存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型路径不存在: {self.model_path}")
            
            # 加载MLX模型和tokenizer
            self.model, self.tokenizer = mlx_lm.load(self.model_path)
            print(f"✅ MLX模型加载成功")
            
        except Exception as e:
            print(f"❌ MLX模型加载失败: {e}")
            raise
    
    def _load_prompt_templates(self) -> Dict[str, Any]:
        """加载prompt模板文件"""
        try:
            # 从当前目录加载prompt模板
            current_dir = os.path.dirname(os.path.abspath(__file__))
            prompts_file = os.path.join(current_dir, "prompts.json")
            
            with open(prompts_file, 'r', encoding='utf-8') as f:
                templates = json.load(f)
            
            print("✅ prompt模板加载成功")
            return templates
        except Exception as e:
            print(f"⚠️ 加载prompt模板失败: {e}")
            # 如果加载失败，返回空字典，程序仍然可以运行（使用默认的硬编码prompt）
            return {}
    
    def _detect_platform_from_text(self, text: str) -> Optional[str]:
        """从自然语言中检测平台"""
        text_lower = text.lower()
        
        # iOS关键词
        ios_keywords = ['ios', 'iphone', 'ipad', '苹果', 'apple', 'ios设备']
        # Android关键词  
        android_keywords = ['android', '安卓', 'google', 'android设备']
        
        ios_count = sum(1 for keyword in ios_keywords if keyword in text_lower)
        android_count = sum(1 for keyword in android_keywords if keyword in text_lower)
        
        if ios_count > android_count:
            return "ios"
        elif android_count > ios_count:
            return "android"
        else:
            return None  # 无法确定
    
    def _load_platform_state(self) -> str:
        """从文件中加载上次使用的平台状态"""
        try:
            if os.path.exists(self._platform_state_file):
                with open(self._platform_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get("last_platform", "android")
            else:
                # 文件不存在，返回默认值
                return "android"
        except Exception as e:
            print(f"⚠️ 读取平台状态文件失败: {e}")
            return "android"
    
    def _save_platform_state(self, platform: str) -> None:
        """保存当前平台状态到文件"""
        try:
            data = {"last_platform": platform}
            with open(self._platform_state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存平台状态文件失败: {e}")
    
    def _get_next_platform(self) -> str:
        """获取下一个平台（轮询方式）"""
        with self._platform_lock:
            # 读取上次使用的平台
            last_platform = self._load_platform_state()
            
            # 轮询到下一个平台
            if last_platform == "ios":
                next_platform = "android"
            else:
                next_platform = "ios"
            
            # 保存新的平台状态
            self._save_platform_state(next_platform)
            
            return next_platform
    
    def determine_platform(self, natural_language: str, specified_platform: Optional[str] = None) -> str:
        """确定目标平台"""
        # 1. 如果明确指定了平台，直接使用
        if specified_platform and specified_platform.lower() in ["ios", "android"]:
            platform = specified_platform.lower()
            print(f"🎯 使用指定平台: {platform.upper()}")
            return platform
        
        # 2. 尝试从自然语言中检测平台
        detected_platform = self._detect_platform_from_text(natural_language)
        if detected_platform:
            print(f"🔍 从文本中检测到平台: {detected_platform.upper()}")
            return detected_platform
        
        # 3. 使用轮询机制
        next_platform = self._get_next_platform()
        print(f"🔄 轮询选择平台: {next_platform.upper()}")
        return next_platform
        
    def _create_tool_mapping(self) -> Dict[str, Dict]:
        """创建工具映射表"""
        return {
            "find_available_device": {
                "description": "查找可用的测试设备",
                "required_params": ["platform"],
                "optional_params": [],
                "example": {"platform": "ios"}
            },
            "ocr_text_validation": {
                "description": "通过OCR搜索包含指定文本的元素信息，返回元素位置和详细信息",
                "required_params": ["udid", "target_text"],
                "optional_params": [],
                "example": {"udid": "{device_udid}", "target_text": "外卖"}
            },
            "start_device_test": {
                "description": "开始设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "take_screenshot": {
                "description": "对设备进行截图",
                "required_params": ["udid"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "description": "首页截图"}
            },
            "tap_device": {
                "description": "点击屏幕指定位置",
                "required_params": ["udid", "x", "y"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "x": 100, "y": 200, "description": "点击搜索框"}
            },
            "slide_device": {
                "description": "滑动屏幕",
                "required_params": ["udid", "from_x", "from_y", "to_x", "to_y"],
                "optional_params": ["duration", "description"],
                "example": {"udid": "{device_udid}", "from_x": 0.5, "from_y": 0.8, "to_x": 0.5, "to_y": 0.2, "duration": 0.5, "description": "向上滑动"}
            },
            "input_text_smart": {
                "description": "在输入框中输入文字",
                "required_params": ["udid", "text"],
                "optional_params": ["element_index"],
                "example": {"udid": "{device_udid}", "text": "北京", "element_index": 0}
            },
            "wait_seconds": {
                "description": "等待指定秒数",
                "required_params": ["seconds"],
                "optional_params": [],
                "example": {"seconds": 3}
            },
            "check_page_display": {
                "description": "检查页面是否存在UI bug或显示异常",
                "required_params": ["udid"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "scene_desc": "检查首页UI bug"}
            },
            "find_element_on_page": {
                "description": "从页面layout信息和截图中查找指定元素的位置和详细信息",
                "required_params": ["udid", "element"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "element": "搜索框", "scene_desc": "从首页布局中定位搜索框"}
            },
            "find_text_on_page": {
                "description": "获取当前页面的OCR结果（带坐标信息），用于查找固定文本的坐标",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "restart_application": {
                "description": "重启应用",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "end_device_test": {
                "description": "结束设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "analyze_meituan_page": {
                "description": "分析美团app界面截图，识别当前界面特征和界面类型",
                "required_params": ["udid"],
                "optional_params": ["action_description", "model"],
                "example": {"udid": "{device_udid}", "action_description": "点击搜索框后", "model": "qwen2.5vl:3b"}
            }
        }
    
    def _create_system_prompt(self) -> str:
        """创建系统提示词（基于JSON模板）"""
        # 如果没有加载模板，则使用默认的逻辑（保持向后兼容）
        if not self.prompt_templates:
            return self._create_system_prompt_fallback()
        
        try:
            template = self.prompt_templates.get("system_prompt_template", {})
            
            # 构建工具信息
            tools_info = []
            for action, info in self.tool_mapping.items():
                tools_info.append(f"- {action}: {info['description']}")
                tools_info.append(f"  必需参数: {info['required_params']}")
                tools_info.append(f"  可选参数: {info['optional_params']}")
                tools_info.append(f"  示例: {json.dumps(info['example'], ensure_ascii=False)}")
                tools_info.append("")
            tools_text = "\n".join(tools_info)
            
            # 构建系统提示词的各个部分
            prompt_parts = []
            
            # 角色描述
            prompt_parts.append(template.get("role_description", ""))
            prompt_parts.append("")
            
            # 可用操作类型
            available_ops = template.get("available_operations", {})
            prompt_parts.append(available_ops.get("header", ""))
            prompt_parts.append(tools_text)
            prompt_parts.append("")
            
            # 测试规则
            test_rules = template.get("test_rules", {})
            prompt_parts.append(test_rules.get("header", ""))
            prompt_parts.append("")
            
            # 各个子规则
            for rule_key in ["session_management", "coordinate_system", "page_check_tools", 
                           "text_recognition", "text_input_process", "page_state_analysis"]:
                rule_section = test_rules.get(rule_key, {})
                if rule_section:
                    prompt_parts.append(rule_section.get("title", ""))
                    for rule in rule_section.get("rules", []):
                        prompt_parts.append(rule)
                    prompt_parts.append("")
            
            # 重要规则
            important_rules = template.get("important_rules", {})
            if important_rules:
                prompt_parts.append(important_rules.get("header", ""))
                for rule in important_rules.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 输出格式
            output_format = template.get("output_format", {})
            if output_format:
                prompt_parts.append(output_format.get("header", ""))
                prompt_parts.append(output_format.get("description", ""))
                prompt_parts.append("")
                
                # JSON模板
                json_template = output_format.get("json_template", {})
                if json_template:
                    json_str = json.dumps(json_template, ensure_ascii=False, indent=2)
                    prompt_parts.append(json_str)
                    prompt_parts.append("")
            
            # 参数类型说明
            param_types = template.get("parameter_types", {})
            if param_types:
                prompt_parts.append(param_types.get("header", ""))
                for rule in param_types.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 参数来源说明
            param_sources = template.get("parameter_sources", {})
            if param_sources:
                prompt_parts.append(param_sources.get("header", ""))
                for rule in param_sources.get("rules", []):
                    prompt_parts.append(rule)
                prompt_parts.append("")
            
            # 示例
            examples = template.get("examples", {})
            if examples:
                prompt_parts.append(examples.get("header", ""))
                prompt_parts.append(examples.get("example_input", ""))
                prompt_parts.append("")
                
                for example in examples.get("example_steps", []):
                    prompt_parts.append(example.get("description", ""))
                    example_json = example.get("json_example", {})
                    if example_json:
                        json_str = json.dumps(example_json, ensure_ascii=False, indent=2)
                        prompt_parts.append("```json")
                        prompt_parts.append(json_str)
                        prompt_parts.append("```")
                    prompt_parts.append("")
                
                # 关键区别
                key_diff = examples.get("key_differences", {})
                if key_diff:
                    prompt_parts.append(key_diff.get("header", ""))
                    for rule in key_diff.get("rules", []):
                        prompt_parts.append(rule)
                    prompt_parts.append("")
            
            # 最终指令
            final_instruction = template.get("final_instruction", "")
            if final_instruction:
                prompt_parts.append(final_instruction)
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            print(f"⚠️ 使用JSON模板构建prompt失败: {e}")
            return self._create_system_prompt_fallback()
    
    def _create_system_prompt_fallback(self) -> str:
        """当JSON模板加载失败时的fallback方法"""
        tools_info = []
        for action, info in self.tool_mapping.items():
            tools_info.append(f"- {action}: {info['description']}")
            tools_info.append(f"  必需参数: {info['required_params']}")
            tools_info.append(f"  可选参数: {info['optional_params']}")
            tools_info.append(f"  示例: {json.dumps(info['example'], ensure_ascii=False)}")
            tools_info.append("")
        
        tools_text = "\n".join(tools_info)
        
        return f"""你是一个专业的移动应用测试计划生成器。你的任务是将用户提供的自然语言测试用例转换为结构化的JSON执行计划。

**可用的操作类型：**
{tools_text}

请严格按照JSON格式输出，确保可以被Python的json.loads()正确解析。"""

    def generate_plan(self, natural_language_request: str, platform: Optional[str] = None, debug_mode: bool = False, use_original_plan: bool = False) -> Dict[str, Any]:
        """将自然语言请求转换为结构化执行计划"""
        print(f"🔒 请求获取模型锁，当前线程: {threading.current_thread().name}")
        
        # 记录总开始时间
        total_start_time = time.time()
        
        # 使用线程锁确保并发安全
        with self._model_lock:
            try:
                print(f"✅ 已获取模型锁，开始处理请求: {threading.current_thread().name}")
                print(f"📝 处理自然语言请求: {natural_language_request}")
                
                # 确定目标平台
                target_platform = self.determine_platform(natural_language_request, platform)
                print(f"📱 确定目标平台: {target_platform.upper()}")
                
                start_time = time.time()
                
                # 构建系统提示词（无RAG增强）
                system_prompt = self._create_system_prompt()
                
                # 构建用户提示词
                user_prompt_template = self.prompt_templates.get("user_prompt_template", 
                    "请将以下测试用例转换为结构化执行计划：\n\n目标平台: {target_platform}\n测试用例: {natural_language_request}")
                user_prompt = user_prompt_template.format(
                    target_platform=target_platform.upper(),
                    natural_language_request=natural_language_request
                )
                
                # 合并系统提示词和用户提示词为单一文本
                full_prompt = f"{system_prompt}\n\n{user_prompt}"
                
                print(f"🤖 调用MLX模型进行计划生成...")
                print(f"📋 完整提示词长度: {len(full_prompt)} 字符")
                
                # Debug模式：输出完整的提示词
                if debug_mode:
                    print(f"\n{'='*80}")
                    print("📄 完整提示词内容：")
                    print("="*80)
                    print(full_prompt)
                    print("="*80)
                    print()
                else:
                    print(f"📄 用户提示词:")
                    print(user_prompt)
                
                # 调用MLX模型 - 使用流式生成with实时停止检测
                generation_result = self._generate_with_streaming_stop(
                    full_prompt=full_prompt,
                    stop_tokens=['<|endoftext|>', '</s>', '<|end|>'],  # 添加多种可能的结束标记
                    show_progress=True  # 显示生成进度，保持用户体验
                )
                
                # 处理生成结果
                if isinstance(generation_result, dict):
                    response_text = generation_result.get('text', '')
                    json_detected = generation_result.get('json_detected', False)
                    detected_json = generation_result.get('detected_json', None)
                else:
                    response_text = generation_result
                    json_detected = False
                    detected_json = None
                
                duration = time.time() - start_time
                print(f"⏱️  MLX模型调用完成，耗时: {duration:.2f}秒")
                print(f"🔍 模型原始响应长度: {len(response_text)} 字符")
                print(f"📄 模型完整响应:")
                print(response_text)
                
                # 优先使用流式检测到的JSON，否则使用传统提取方法
                if json_detected and detected_json:
                    print(f"🎯 使用流式检测到的JSON，跳过传统提取")
                    json_data = detected_json
                else:
                    print(f"🔍 使用传统JSON提取方法")
                    json_data = self._extract_json_from_response(response_text)
                
                if json_data:
                    # 确保平台信息正确
                    json_data["platform"] = target_platform
                    print(f"✅ JSON解析成功，生成了 {json_data.get('total_steps', 0)} 个步骤")
                    
                    # 直接使用原始计划（无RAG优化）
                    final_plan = json_data
                    
                    # 计算总耗时
                    total_duration = time.time() - total_start_time
                    print(f"⏱️  🎯 总耗时: {total_duration:.2f}秒")
                    
                    return {
                        "status": "success",
                        "plan": final_plan,
                        "raw_response": response_text,
                        "processing_time": duration,
                        "total_time": total_duration,
                        "detected_platform": target_platform,
                        "rag_enabled": False,
                        "system_prompt": system_prompt if debug_mode else None,
                        "user_prompt": user_prompt if debug_mode else None,
                        "model_info": {
                            "framework": "MLX",
                            "model_path": self.model_path,
                            "temperature": "default (0.0)",
                            "max_tokens": self.max_tokens
                        }
                    }
                else:
                    print("❌ 未找到有效的JSON格式")
                    print(f"原始响应: {response_text}")
                    
                    # 计算总耗时
                    total_duration = time.time() - total_start_time
                    print(f"⏱️  🎯 总耗时: {total_duration:.2f}秒")
                    
                    return {
                        "status": "error", 
                        "error": "未找到有效的JSON格式",
                        "raw_response": response_text,
                        "processing_time": duration,
                        "total_time": total_duration,
                        "system_prompt": system_prompt if debug_mode else None,
                        "user_prompt": user_prompt if debug_mode else None,
                        "model_info": {
                            "framework": "MLX",
                            "model_path": self.model_path,
                            "temperature": "default (0.0)",
                            "max_tokens": self.max_tokens
                        }
                    }
                    
            except Exception as e:
                print(f"❌ 生成计划时发生异常: {str(e)}")
                
                # 计算总耗时（即使出错也要统计）
                total_duration = time.time() - total_start_time
                print(f"⏱️  🎯 总耗时: {total_duration:.2f}秒")
                
                return {
                    "status": "error",
                    "error": f"生成计划时发生异常: {str(e)}",
                    "raw_response": "",
                    "processing_time": 0,
                    "total_time": total_duration,
                    "system_prompt": locals().get('system_prompt') if debug_mode else None,
                    "user_prompt": locals().get('user_prompt') if debug_mode else None,
                    "model_info": {
                        "framework": "MLX",
                        "model_path": self.model_path,
                        "temperature": "default (0.0)",
                        "max_tokens": self.max_tokens
                    }
                }
            finally:
                print(f"🔓 释放模型锁: {threading.current_thread().name}")
    
    def _generate_with_streaming_stop(self, full_prompt: str, stop_tokens: List[str] = None, show_progress: bool = True) -> str:
        """
        使用流式生成，实时检测停止标记并立即停止生成
        支持检测 ```json 标记和完整JSON结构，自动停止生成
        
        Args:
            full_prompt: 完整的提示词
            stop_tokens: 停止标记列表，默认包含 '<|endoftext|>'
            show_progress: 是否显示生成进度
        
        Returns:
            生成的文本（截止到停止标记之前）
        """
        if stop_tokens is None:
            stop_tokens = ['<|endoftext|>', '</s>']  # 添加多种可能的结束标记
        
        print(f"🔄 开始流式生成，监控停止标记: {stop_tokens}")
        print(f"🔍 同时监控JSON块标记: ```json 和完整JSON结构")
        
        try:
            # 使用MLX的stream_generate进行流式生成
            generated_text = ""
            detection_buffer = ""  # 用于检测跨token的停止标记
            buffer_size = 50  # 增大检测缓冲区大小以支持```json检测
            token_count = 0
            
            # JSON块检测相关变量
            json_block_detected = False  # 是否检测到```json标记
            json_content_buffer = ""     # JSON内容缓冲区
            brace_count = 0             # 大括号匹配计数
            json_start_marker = "```json"  # JSON块开始标记
            json_collecting = False      # 是否正在收集JSON内容
            json_first_brace_found = False  # 是否找到第一个{
            json_validation_attempts = 0  # JSON验证尝试次数
            last_validation_length = 0   # 上次验证时的长度
            
            # 开始流式生成
            stream = mlx_lm.stream_generate(
                model=self.model,
                tokenizer=self.tokenizer,
                prompt=full_prompt,
                max_tokens=self.max_tokens
            )
            
            print(f"📺 流式生成开始...")
            if show_progress:
                print("="*10)  # 添加分隔符，让内容更清晰
            start_time = time.time()
            
            for response in stream:
                # MLX stream_generate返回GenerationResponse对象，需要获取其text属性
                if hasattr(response, 'text') and response.text:
                    token_text = response.text
                elif isinstance(response, str):
                    token_text = response
                else:
                    # 跳过无效的响应
                    continue
                
                if token_text:
                    # 添加到生成的文本
                    generated_text += token_text
                    detection_buffer += token_text
                    token_count += 1
                    
                    # 实时显示生成的内容（保持用户体验一致）
                    if show_progress:
                        print(token_text, end='', flush=True)
                    
                    # 保持检测缓冲区大小
                    if len(detection_buffer) > buffer_size:
                        detection_buffer = detection_buffer[-buffer_size:]
                    
                    # 1. 检测```json标记
                    if not json_block_detected and json_start_marker in detection_buffer:
                        json_block_detected = True
                        json_collecting = True
                        print(f"\n🎯 检测到JSON块开始标记: {json_start_marker}")
                        
                        # 找到```json标记的位置，从该位置开始收集JSON内容
                        json_marker_pos = generated_text.rfind(json_start_marker)
                        if json_marker_pos != -1:
                            # 从```json标记之后开始收集内容
                            content_after_marker = generated_text[json_marker_pos + len(json_start_marker):]
                            json_content_buffer = content_after_marker
                    
                    # 2. 如果正在收集JSON内容
                    if json_collecting:
                        # 将新token添加到JSON内容缓冲区
                        if json_block_detected:
                            json_content_buffer += token_text
                        
                        # 处理每个新字符
                        for char in token_text:
                            # 检测第一个{来开始大括号计数
                            if not json_first_brace_found and char == '{':
                                json_first_brace_found = True
                                brace_count = 1  # 第一个{计为1
                                print(f"\n📝 检测到JSON开始标记{{，开始大括号计数...")
                                print(f"🔢 当前计数: {brace_count}")
                                continue
                            
                            # 如果已经开始计数，继续处理大括号
                            if json_first_brace_found:
                                if char == '{':
                                    brace_count += 1
                                elif char == '}':
                                    brace_count -= 1
                                    
                                    # 如果计数回到0，说明从第一个{开始的JSON结构完整
                                    if brace_count == 0:
                                        print(f"\n🎯 大括号计数归零！提取完整JSON进行验证...")
                                        
                                        # 提取从第一个{开始到当前}的完整JSON
                                        json_start_pos = json_content_buffer.find('{')
                                        if json_start_pos != -1:
                                            # 计算当前}在json_content_buffer中的位置
                                            current_pos = len(json_content_buffer) - len(token_text) + (token_text.find(char) + 1)
                                            complete_json = json_content_buffer[json_start_pos:current_pos]
                                            
                                            print(f"📏 提取JSON长度: {len(complete_json)} 字符")
                                            print(f"🔍 JSON开头: {complete_json[:100]}...")
                                            print(f"🔍 JSON结尾: {complete_json[-100:]}")
                                            
                                            # 验证JSON完整性
                                            if self._validate_json_completeness(complete_json):
                                                print(f"\n✅ JSON验证通过，自动停止生成！")
                                                elapsed = time.time() - start_time
                                                print(f"🏁 流式生成已停止 - 完整JSON检测")
                                                print(f"📊 生成统计: {token_count} tokens, {len(generated_text)} 字符, 耗时: {elapsed:.2f}秒")
                                                
                                                # 解析检测到的JSON
                                                try:
                                                    parsed_json = json.loads(complete_json)
                                                    # 立即修复total_steps计数问题
                                                    parsed_json = self._fix_total_steps(parsed_json)
                                                    return {
                                                        'text': generated_text,
                                                        'json_detected': True,
                                                        'detected_json': parsed_json
                                                    }
                                                except json.JSONDecodeError as e:
                                                    print(f"⚠️ JSON解析失败: {e}")
                                                    return generated_text
                                            else:
                                                print(f"⚠️ JSON验证失败，这可能是嵌套对象的结束，继续等待...")
                                                # 计数已经是0，继续等待后续的{或}
                                        else:
                                            print(f"❌ 未找到JSON起始位置")
                                # 继续处理下一个字符
                    
                    # 3. 检测传统停止标记
                    stop_detected = False
                    detected_token = None
                    for stop_token in stop_tokens:
                        if stop_token in detection_buffer or stop_token in generated_text[-len(stop_token):]:
                            print(f"\n🛑 检测到停止标记: {stop_token}")
                            stop_detected = True
                            detected_token = stop_token
                            
                            # 截断到停止标记之前的内容
                            stop_index = generated_text.rfind(stop_token)
                            if stop_index > 0:
                                generated_text = generated_text[:stop_index]
                                print(f"\n✂️ 文本截断到停止标记之前，最终长度: {len(generated_text)} 字符")
                            break
                    
                    # 如果检测到传统停止标记，立即停止
                    if stop_detected:
                        elapsed = time.time() - start_time
                        print(f"🏁 流式生成已停止 - 检测到: {detected_token}")
                        print(f"📊 生成统计: {token_count} tokens, {len(generated_text)} 字符, 耗时: {elapsed:.2f}秒")
                        break
            else:
                # 正常结束（没有检测到停止标记）
                elapsed = time.time() - start_time
                print(f"✅ 流式生成完成（自然结束）")
                print(f"📊 生成统计: {token_count} tokens, {len(generated_text)} 字符, 耗时: {elapsed:.2f}秒")
            
            return generated_text
            
        except Exception as e:
            print(f"❌ 流式生成过程中发生错误: {e}")
            print(f"💾 返回已生成的部分内容: {len(locals().get('generated_text', '')) if 'generated_text' in locals() else 0} 字符")
            return locals().get('generated_text', '')
    
    def _fix_total_steps(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """统一修复total_steps计数问题"""
        if 'steps' in json_data and 'total_steps' in json_data:
            actual_steps = len(json_data['steps'])
            if json_data['total_steps'] != actual_steps:
                print(f"🔧 修复total_steps计数: {json_data['total_steps']} -> {actual_steps}")
                json_data['total_steps'] = actual_steps
        return json_data
    
    def _validate_json_completeness(self, json_content: str) -> bool:
        """
        验证JSON内容的完整性和有效性（宽松模式）
        
        Args:
            json_content: 待验证的JSON内容字符串
            
        Returns:
            bool: 如果JSON完整且有效则返回True
        """
        try:
            # 清理JSON内容，移除可能的前后空白和换行
            cleaned_content = json_content.strip()
            
            # 基本检查：必须以{开始，以}结束
            if not (cleaned_content.startswith('{') and cleaned_content.endswith('}')):
                print(f"🔍 JSON格式检查失败: 不是以{{开始和}}结束")
                return False
            
            # 尝试解析JSON
            try:
                parsed_json = json.loads(cleaned_content)
            except json.JSONDecodeError as e:
                print(f"🔍 JSON解析失败: {str(e)[:100]}")
                return False
            
            # 宽松验证：只检查最基本的字段
            if not isinstance(parsed_json, dict):
                print(f"🔍 JSON不是对象类型")
                return False
            
            # 检查是否包含plan相关的关键字段（至少包含其中2个）
            plan_indicators = ['plan_id', 'steps', 'total_steps', 'original_request', 'summary']
            found_indicators = sum(1 for field in plan_indicators if field in parsed_json)
            
            if found_indicators < 2:
                print(f"🔍 计划相关字段不足: 找到{found_indicators}个，需要至少2个")
                return False
            
            # 如果有steps字段，简单检查一下
            if 'steps' in parsed_json:
                steps = parsed_json.get('steps')
                if not isinstance(steps, list):
                    print(f"🔍 steps字段不是列表类型")
                    return False
                
                if len(steps) == 0:
                    print(f"🔍 steps列表为空")
                    return False
                
                # 只检查第一个步骤的基本结构
                if len(steps) > 0:
                    first_step = steps[0]
                    if not isinstance(first_step, dict):
                        print(f"🔍 第一个步骤不是字典类型")
                        return False
                    
                    # 检查是否有基本的步骤字段
                    basic_fields = ['step_id', 'action', 'description']
                    missing_fields = [field for field in basic_fields if field not in first_step]
                    if len(missing_fields) > 1:  # 允许缺少1个字段
                        print(f"🔍 第一个步骤缺少过多基本字段: {missing_fields}")
                        return False
            
            steps_count = len(parsed_json.get('steps', [])) if 'steps' in parsed_json else 0
            print(f"✅ JSON验证通过: {steps_count}个步骤，包含{found_indicators}个计划指标")
            return True
            
        except Exception as e:
            print(f"🔍 JSON验证异常: {str(e)}")
            return False
    
    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从响应中提取JSON，使用多种策略，优先选择最靠近endoftext的有效JSON"""
        print(f"🔍 开始JSON提取，文本长度: {len(response_text)} 字符")
        
        # 策略0: 清理思维链标签
        cleaned_response = response_text
        # 移除 <think>...</think> 标签
        think_pattern = r'<think>.*?</think>'
        cleaned_response = re.sub(think_pattern, '', cleaned_response, flags=re.DOTALL)
        
        # 预处理：检查是否包含endoftext标记，如果包含则只处理该标记之前的内容
        end_tokens = ['<|endoftext|>', '</s>', '<|end|>']
        end_position = len(cleaned_response)
        found_end_token = None
        
        for token in end_tokens:
            pos = cleaned_response.find(token)
            if pos != -1 and pos < end_position:
                end_position = pos
                found_end_token = token
        
        if found_end_token:
            print(f"✂️ 发现结束标记 '{found_end_token}' 在位置 {end_position}，截断处理")
            cleaned_response = cleaned_response[:end_position]
        
        # 定义执行计划的关键字特征
        plan_keywords = ['original_request', 'total_steps', 'platform', 'plan_id', 'steps', 'summary']
        
        def has_plan_keywords(text: str) -> bool:
            """检查文本是否包含执行计划的关键字（至少包含3个核心字段）"""
            count = sum(1 for keyword in plan_keywords if keyword in text)
            return count >= 3  # 至少包含3个关键字才认为是执行计划
        
        # 收集所有可能的JSON候选
        json_candidates = []
        
        # 策略1: 查找代码块中的JSON（优先，且必须包含关键字）
        code_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        code_matches = re.finditer(code_block_pattern, cleaned_response, re.DOTALL | re.IGNORECASE)
        for match in code_matches:
            content = match.group(1)
            if has_plan_keywords(content):  # 只有包含关键字的才作为候选
                json_candidates.append({
                    'content': content,
                    'start_pos': match.start(),
                    'end_pos': match.end(),
                    'source': 'code_block',
                    'priority': 1  # 代码块中的JSON优先级最高
                })
        
        # 策略2: 如果代码块中没找到合适的JSON，才使用直接匹配（且需要关键字筛选）
        if not json_candidates:  # 只有在没有找到代码块JSON时才使用直接匹配
            print("🔍 代码块中未找到有效JSON，使用直接匹配策略")
            json_patterns = [
                (r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', 2),  # 简单嵌套，优先级2
                (r'\{.*?\}', 3),  # 最小匹配，优先级3
                (r'\{.*\}', 4),   # 最大匹配，优先级4（最低）
            ]
            
            for pattern, priority in json_patterns:
                pattern_matches = re.finditer(pattern, cleaned_response, re.DOTALL)
                for match in pattern_matches:
                    content = match.group(0)
                    # 只有包含关键字且长度合理的JSON才作为候选
                    if has_plan_keywords(content) and len(content) > 100:  # 至少100字符
                        json_candidates.append({
                            'content': content,
                            'start_pos': match.start(),
                            'end_pos': match.end(),
                            'source': 'direct_match',
                            'priority': priority
                        })
                        
                        # 限制候选数量，避免太多无效候选
                        if len(json_candidates) >= 10:
                            print(f"⚠️ 已收集{len(json_candidates)}个候选，停止继续搜索")
                            break
                
                # 如果已经有候选了，优先处理高优先级的
                if json_candidates and priority <= 3:
                    break
        
        print(f"📋 找到 {len(json_candidates)} 个JSON候选")
        
        # 验证并筛选有效的JSON
        valid_jsons = []
        for i, candidate in enumerate(json_candidates):
            try:
                cleaned_json = self._clean_json_string(candidate['content'])
                data = json.loads(cleaned_json)
                
                if self._validate_plan_structure(data):
                    valid_jsons.append({
                        **candidate,
                        'parsed_data': data,
                        'cleaned_content': cleaned_json
                    })
                    print(f"✅ 候选 {i+1}: 有效JSON (优先级: {candidate['priority']}, 位置: {candidate['start_pos']}-{candidate['end_pos']})")
                else:
                    print(f"❌ 候选 {i+1}: JSON结构验证失败")
            except json.JSONDecodeError as e:
                print(f"❌ 候选 {i+1}: JSON解析失败 - {str(e)[:50]}")
                continue
        
        if not valid_jsons:
            print("😞 没有找到有效的JSON")
            return None
        
        # 选择最佳JSON：优先级高的，如果优先级相同则选择位置最靠后的（最接近endoftext）
        best_json = max(valid_jsons, key=lambda x: (-x['priority'], x['start_pos']))
        
        print(f"🎯 选择最佳JSON: 优先级 {best_json['priority']}, 位置 {best_json['start_pos']}-{best_json['end_pos']}")
        print(f"📊 计划步骤数: {best_json['parsed_data'].get('total_steps', 0)}")
        
        return best_json['parsed_data']
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        # 移除前后空白
        json_str = json_str.strip()
        
        # 修复常见的格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        
        # 尝试解析并重新格式化JSON
        try:
            # 先尝试直接解析
            import json
            data = json.loads(json_str)
            
            # 修复 total_steps 计数问题
            data = self._fix_total_steps(data)
            
            # 重新序列化
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        except json.JSONDecodeError:
            # 如果解析失败，尝试修复常见问题
            # 确保字符串值被正确引用（但要小心已经引用的值）
            json_str = re.sub(r':\s*([^",\[\]{}]+)\s*([,}])', r': "\1"\2', json_str)
            
            return json_str
    
    def _validate_plan_structure(self, data: Dict[str, Any]) -> bool:
        """验证计划结构是否有效"""
        required_fields = ['plan_id', 'steps', 'total_steps']
        
        for field in required_fields:
            if field not in data:
                return False
        
        if not isinstance(data['steps'], list):
            return False
            
        if len(data['steps']) == 0:
            return False
            
        # 验证步骤结构
        for step in data['steps']:
            if not isinstance(step, dict):
                return False
            step_required = ['step_id', 'action', 'description', 'parameters']
            for field in step_required:
                if field not in step:
                    return False
        
        return True
    
    def convert_plan_to_agent_instruction(self, plan: Dict[str, Any]) -> str:
        """将结构化计划转换为Agent可以理解的指令"""
        try:
            if not plan or "steps" not in plan:
                return "错误：计划格式无效"
            
            instructions = []
            instructions.append(f"请按照以下步骤执行测试计划：")
            instructions.append(f"计划摘要：{plan.get('summary', '未知')}")
            instructions.append("")
            
            for i, step in enumerate(plan["steps"], 1):
                step_desc = step.get("description", "")
                expected = step.get("expected_result", "")
                
                instruction = f"{i}. {step_desc}"
                if expected:
                    instruction += f"，预期结果：{expected}"
                instructions.append(instruction)
            
            instructions.append("")
            instructions.append("请严格按照上述步骤顺序执行，每完成一个步骤后再进行下一步。")
            
            return "\n".join(instructions)
            
        except Exception as e:
            return f"转换指令时发生错误: {str(e)}"
    
    def convert_plan_to_detailed_instruction(self, plan: Dict[str, Any]) -> str:
        """将结构化计划转换为包含具体工具调用的详细指令"""
        try:
            if not plan or "steps" not in plan:
                return "错误：计划格式无效"
            
            instructions = []
            instructions.append("=" * 80)
            instructions.append("🎯 测试计划摘要")
            instructions.append("=" * 80)
            instructions.append(f"📝 计划描述: {plan.get('summary', '未知')}")
            instructions.append(f"📱 目标平台: {plan.get('platform', 'unknown').upper()}")
            instructions.append(f"🔢 总步骤数: {plan.get('total_steps', len(plan['steps']))}")
            instructions.append(f"📋 计划ID: {plan.get('plan_id', 'unknown')}")
            instructions.append("")
            instructions.append("=" * 80)
            instructions.append("📋 详细执行步骤")
            instructions.append("=" * 80)
            
            for i, step in enumerate(plan.get("steps", [])):
                step_id = step.get("step_id", 0)
                action = step.get("action", "")
                description = step.get("description", "")
                parameters = step.get("parameters", {})
                parameter_types = step.get("parameter_types", {})
                parameter_sources = step.get("parameter_sources", {})
                expected = step.get("expected_result", "")
                
                # 直接使用action作为工具名（已经是原始工具名）
                tool_name = action
                
                instructions.append(f"步骤 {step_id}: {description}")
                instructions.append(f"   🔧 工具调用: {tool_name}")
                instructions.append(f"   📝 参数: {json.dumps(parameters, ensure_ascii=False)}")
                
                # 添加参数类型分析
                if parameter_types:
                    static_params = {k: v for k, v in parameters.items() if parameter_types.get(k) == "static"}
                    dynamic_params = {k: v for k, v in parameters.items() if parameter_types.get(k) == "dynamic"}
                    
                    if static_params:
                        instructions.append(f"   🔹 固定值参数: {json.dumps(static_params, ensure_ascii=False)}")
                    if dynamic_params:
                        instructions.append(f"   🔸 动态值参数: {json.dumps(dynamic_params, ensure_ascii=False)}")
                        
                        # 显示动态参数的来源
                        for param_name in dynamic_params:
                            source = parameter_sources.get(param_name, "未知来源")
                            instructions.append(f"     └─ {param_name}: {source}")
                
                instructions.append(f"   ✅ 预期结果: {expected}")
                
                # # 增强步骤间的关联性说明
                # if i < len(plan.get("steps", [])) - 1:
                #     next_step = plan.get("steps", [])[i + 1]
                #     next_action = next_step.get("action", "")
                    
                #     # 特定的步骤关联提示
                #     if action == "find_element_on_page" and next_action == "tap_device":
                #         instructions.append(f"   🔗 **重要**: 下一步将点击本步骤找到的元素位置")
                #     elif action == "tap_device" and next_action == "check_page_display":
                #         instructions.append(f"   🔗 **关联**: 点击后需要验证页面变化结果")
                #     elif action == "input_text_smart" and next_action == "find_element_on_page":
                #         instructions.append(f"   🔗 **关联**: 输入文本后将查找相关的搜索结果")
                #     elif action in ["find_element_on_page", "tap_device"] and "udid" in parameters:
                #         instructions.append(f"   🔗 **设备关联**: 在设备 {parameters.get('udid', '{device_udid}')} 上执行")
                
                # instructions.append("")
            
            instructions.append("=" * 80)
            instructions.append("⚠️  重要执行要求")
            instructions.append("=" * 80)
            instructions.append("• **步骤顺序**: 严格按照步骤顺序执行，一次执行一个工具")
            instructions.append("• **等待结果**: 每个工具执行完成后，等待结果再进行下一步")
            instructions.append("• **参数类型区分**: ")
            instructions.append("  - 🔹 固定值参数: 直接使用，不需要替换")
            instructions.append("  - 🔸 动态值参数: 必须从指定的步骤结果中获取实际值")
            instructions.append("• **动态参数获取**: 根据parameter_sources指示，从对应步骤结果中提取实际值")
            instructions.append("• **步骤关联**: 特别注意find_element_on_page找到的元素位置，在后续tap_device中使用")
            instructions.append("• **坐标使用**: tap_device操作时，优先使用find_element_on_page返回的坐标信息")
            instructions.append("• **设备一致性**: 确保所有操作都在同一设备(udid)上执行")
            instructions.append("• **失败重试**: 如果某个步骤失败，尝试1-2次后再继续")
            instructions.append("• **完成记录**: 完成所有步骤后调用record_agent_summary记录总结")
            instructions.append("=" * 80)
            
            return "\n".join(instructions)
            
        except Exception as e:
            return f"转换详细指令时发生错误: {str(e)}"
    
    def _save_plan_to_log(self, original_request: str, result: Dict[str, Any], save_logs: bool = True) -> Optional[str]:
        """将生成的计划保存到日志文件"""
        if not save_logs or not self.enable_logs or not self.log_manager:
            return None
            
        try:
            # 生成时间戳文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f"plan_mlx_{timestamp}.json"
            log_filepath = os.path.join(self.log_manager.log_dir, log_filename)
            
            # 构建日志数据
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "original_request": original_request,
                "generation_result": {
                    "status": result.get("status"),
                    "processing_time": result.get("processing_time", 0),
                    "detected_platform": result.get("detected_platform"),
                    "rag_enabled": result.get("rag_enabled", False),
                    "model_info": result.get("model_info", {})
                }
            }
            
            # 如果生成成功，添加计划详情
            if result.get("status") == "success" and "plan" in result:
                plan = result["plan"]
                log_data["structured_plan"] = plan
                log_data["plan_summary"] = {
                    "plan_id": plan.get("plan_id"),
                    "summary": plan.get("summary"),
                    "platform": plan.get("platform"),
                    "total_steps": plan.get("total_steps", 0),
                    "step_count": len(plan.get("steps", []))
                }
                
                # 添加转换后的Agent指令
                agent_instruction = self.convert_plan_to_agent_instruction(plan)
                detailed_instruction = self.convert_plan_to_detailed_instruction(plan)
                log_data["agent_instructions"] = {
                    "simple_instruction": agent_instruction,
                    "detailed_instruction": detailed_instruction
                }
                
                # 无RAG优化结果
                    
                # 使用日志管理器记录成功信息
                self.log_manager.info_agent(f"MLX JSON计划生成成功: {plan.get('plan_id')} ({plan.get('total_steps', 0)}个步骤)")
            else:
                # 如果生成失败，保存错误信息
                log_data["error_info"] = {
                    "error": result.get("error"),
                    "raw_response": result.get("raw_response", "")
                }
                
                # 使用日志管理器记录错误信息
                self.log_manager.error_agent(f"MLX JSON计划生成失败: {result.get('error', 'Unknown error')}")
            
            # 保存到文件
            with open(log_filepath, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            # 使用日志管理器记录保存信息
            self.log_manager.info_agent(f"MLX计划已保存到: {log_filename}")
            print(f"📁 MLX计划已保存到日志: {log_filepath}")
            return log_filepath
            
        except Exception as e:
            error_msg = f"保存MLX计划日志失败: {e}"
            if self.log_manager:
                self.log_manager.error_agent(error_msg)
            print(f"⚠️ {error_msg}")
            return None
    
    def test_plan_generation(self, test_case: str, debug_mode: bool = False, save_logs: bool = True) -> None:
        """测试计划生成功能"""
        print(f"🧪 [MLX] 测试用例: {test_case}")
        print("="*60)
        
        result = self.generate_plan(test_case, debug_mode=debug_mode)
        
        # 保存到日志
        if save_logs:
            log_filepath = self._save_plan_to_log(test_case, result, save_logs)
            if log_filepath:
                result["log_filepath"] = log_filepath
        
        if result["status"] == "success":
            plan = result["plan"]
            print("✅ MLX计划生成成功")
            print(f"📋 计划ID: {plan.get('plan_id', 'unknown')}")
            print(f"📝 摘要: {plan.get('summary', 'unknown')}")
            print(f"🔢 总步骤数: {plan.get('total_steps', 0)}")
            print(f"🤖 使用模型: {result.get('model_info', {}).get('model_path', 'unknown')}")
            print("\n📋 执行步骤:")
            
            for step in plan.get("steps", []):
                print(f"  {step['step_id']}. {step['description']}")
                print(f"     动作: {step['action']}")
                print(f"     参数: {json.dumps(step['parameters'], ensure_ascii=False)}")
                print(f"     预期: {step['expected_result']}")
                print()
            
            # 转换为Agent指令
            agent_instruction = self.convert_plan_to_agent_instruction(plan)
            print("🤖 转换后的Agent指令:")
            print(agent_instruction)
            
        else:
            print("❌ MLX计划生成失败")
            print(f"错误: {result['error']}")
            print(f"原始响应: {result['raw_response']}")


def main():
    """主函数，用于测试"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Agent JSON规划器测试 (MLX版本)')
    parser.add_argument('--model-path', type=str, 
                       default=DEFAULT_MODEL_PATH,
                       help='MLX模型路径')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--no-logs', action='store_true', help='禁用日志保存')
    parser.add_argument('--max-tokens', type=int, default=DEFAULT_MAX_TOKENS, help='最大生成token数')
    args = parser.parse_args()
    
    enable_rag = False  # 禁用RAG系统
    debug_mode = args.debug
    save_logs = not args.no_logs
    
    print("🚀 启动Agent JSON规划器测试 (MLX版本)")
    print(f"📊 RAG系统: 禁用")
    print(f"🐛 调试模式: {'启用' if debug_mode else '禁用'}")
    print(f"📁 日志保存: {'启用' if save_logs else '禁用'}")
    print(f"🤖 模型路径: {args.model_path}")
    print(f"🌡️  温度参数: 默认 (0.0)")
    print(f"📏 最大tokens: {args.max_tokens}")
    
    # 初始化规划器
    try:
        planer = AgentJsonPlanerMLX(
            model_path=args.model_path,
            max_tokens=args.max_tokens,
            enable_logs=save_logs
        )
    except Exception as e:
        print(f"❌ 初始化MLX规划器失败: {e}")
        return
    
    # 测试用例
    test_cases = [
        # 原有的复杂测试用例
        '校验美团 app 首页存在"资质与规则"文案，确认侧边栏UI展示正常。向上滑动页面，等待2秒后，校验展示资质与规则文案，结束测试',
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"MLX测试案例 {i}")
        print(f"{'='*80}")
        planer.test_plan_generation(test_case, debug_mode=debug_mode, save_logs=save_logs)


if __name__ == "__main__":
    main()