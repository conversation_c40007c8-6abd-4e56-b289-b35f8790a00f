// 数据模型类型定义

// 测试执行记录
export interface TestExecution {
  execution_id: string;
  plan_id: string;
  original_request: string;
  execution_status: 'success' | 'failure' | 'pending' | 'running';
  is_success: boolean;
  overall_success: 'success' | 'failure' | 'partial';
  total_rounds: number;
  total_duration: number;
  start_time: string;
  end_time: string;
  created_at: string;
}

// 测试计划
export interface TestPlan {
  plan_id: string;
  original_request: string;
  platform: string;
  total_steps: number;
  step_count?: number;
  plan_summary: string;
  structured_plan: {
    steps: Array<{
      step: number;
      action: string;
      expected_result?: string;
    }>;
  };
  generation_metadata?: any;
  agent_instructions?: string;
  created_at: string;
  updated_at?: string;
  execution_count?: number; // 关联的执行记录数量
}

// 工具执行记录
export interface ToolExecution {
  execution_id: string;
  round_number: number;
  tool_name: string;
  tool_parameters: any;
  execution_time: number;
  tool_status: 'success' | 'failure' | 'timeout';
  tool_result: any;
  result_summary: string;
  created_at: string;
}

// 综合评价
export interface ComprehensiveEvaluation {
  execution_id: string;
  evaluation_round: string;
  analysis_model: string;
  final_success_status: 'success' | 'failure' | 'partial';
  overall_success_score: number;
  confidence_score: number;
  comprehensive_analysis: {
    plan_coverage_assessment: string;
    major_issues_found: string[];
    execution_highlights: string;
    improvement_priorities: string[];
  } | string;
  evaluation_summary: {
    overall_success: string;
    overall_score: number;
    confidence_level: number;
    key_findings: string[];
  } | string;
  created_at: string;
}

// 统计数据
export interface StatisticsOverview {
  total_plans: number;
  total_executions: number;
  total_tools: number;
  success_rate: number;
  average_duration: number;
  today_executions: number;
  this_week_executions: number;
}

// 成功率趋势数据
export interface SuccessRateTrend {
  date: string;
  success_count: number;
  total_count: number;
  success_rate: number;
}

// 指令分析数据
export interface InstructionAnalysis {
  instruction_pattern: string;
  total_count: number;
  success_count: number;
  success_rate: number;
  average_duration: number;
}

// 数据库表信息
export interface DatabaseTable {
  table_name: string;
  column_count: number;
  record_count: number;
  columns: Array<{
    name: string;
    type: string;
    not_null: boolean;
    default_value: any;
    primary_key: boolean;
  }>;
}

// 表记录数据
export interface TableRecord {
  [key: string]: any;
}